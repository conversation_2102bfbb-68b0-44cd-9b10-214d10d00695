package com.stt.android.suuntoplusstore.features

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.device.R
import com.stt.android.device.suuntoplusdetails.SuuntoPlusItemDetailsHeader
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusDetailsNote
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusItemDetailNotes
import com.stt.android.suuntoplus.SuuntoPlusItemLabel
import com.stt.android.suuntoplus.ui.SuuntoPlusItemDetailsScreenLabels
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import java.util.Locale
import com.stt.android.R as BR

@Suppress("ktlint:compose:modifier-missing-check")
@Composable
fun ColumnScope.SuuntoPlusStoreWatchfaceDetails(
    labels: ImmutableList<SuuntoPlusItemLabel>,
    watchPreviewImageUrl: String?,
    title: String,
    onSetAsCurrentWatchfaceClick: () -> Unit,
    showSetAsCurrentWatchfaceButton: Boolean,
    enableButton: Boolean,
    addToWatchButtonText: String?,
    removeFromWatchButtonText: String?,
    onAddToWatch: () -> Unit,
    onRemoveFromWatch: () -> Unit,
    notes: ImmutableList<SuuntoPlusDetailsNote>,
    onNoteAction: (SuuntoPlusDetailsNote.NoteButtonAction) -> Unit,
) {
    SuuntoPlusItemDetailsHeader(
        bannerImageUrl = null,
        bannerImageScale = ContentScale.Crop,
        watchPreviewImageUrl = watchPreviewImageUrl,
        title = title,
        showWatchPreviewOnly = true,
    )

    SuuntoPlusItemDetailsScreenLabels(
        labels = labels,
        modifier = Modifier.padding(MaterialTheme.spacing.medium)
    )

    SuuntoPlusItemDetailNotes(
        notes = notes,
        onNoteAction = onNoteAction,
        modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
    )

    Spacer(modifier = Modifier.weight(1f))

    WatchfaceButtons(
        enableButton = enableButton,
        addToWatchButtonText = addToWatchButtonText,
        removeFromWatchButtonText = removeFromWatchButtonText,
        onAddToWatch = onAddToWatch,
        onRemoveFromWatch = onRemoveFromWatch,
        showSetAsCurrentWatchfaceButton = showSetAsCurrentWatchfaceButton,
        onSetAsCurrentWatchfaceClick = onSetAsCurrentWatchfaceClick,
    )
}

@Composable
private fun WatchfaceButtons(
    enableButton: Boolean,
    addToWatchButtonText: String?,
    removeFromWatchButtonText: String?,
    onAddToWatch: () -> Unit,
    onRemoveFromWatch: () -> Unit,
    showSetAsCurrentWatchfaceButton: Boolean,
    onSetAsCurrentWatchfaceClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (addToWatchButtonText == null && removeFromWatchButtonText == null) return

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        if (showSetAsCurrentWatchfaceButton) {
            PrimaryButton(
                text = stringResource(id = BR.string.suunto_plus_set_as_current_watch_face),
                enabled = enableButton,
                onClick = { onSetAsCurrentWatchfaceClick() },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium)
            )
        }

        if (removeFromWatchButtonText != null) {
            TextButton(
                onClick = onRemoveFromWatch,
                enabled = enableButton,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colors.error,
                    disabledContentColor = MaterialTheme.colors.error.copy(alpha = 0.6f)
                ),
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(bottom = MaterialTheme.spacing.medium)
            ) {
                Text(
                    text = removeFromWatchButtonText.uppercase(Locale.getDefault()),
                )
            }
        } else if (addToWatchButtonText != null) {
            PrimaryButton(
                text = addToWatchButtonText.uppercase(Locale.getDefault()),
                enabled = enableButton,
                onClick = onAddToWatch,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun SuuntoPlusStoreWatchfaceDetailsPreview() {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            SuuntoPlusStoreWatchfaceDetails(
                labels = persistentListOf(
                    SuuntoPlusItemLabel("My watch faces", color = MaterialTheme.colors.primary),
                ),
                watchPreviewImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/zzbrnr01.jpg",
                title = "Burner",
                onSetAsCurrentWatchfaceClick = {},
                showSetAsCurrentWatchfaceButton = true,
                enableButton = true,
                addToWatchButtonText = null,
                removeFromWatchButtonText = stringResource(id = R.string.suunto_plus_floating_action_button_uninstall_from_watch),
                onAddToWatch = { },
                onRemoveFromWatch = { },
                notes = persistentListOf(SuuntoPlusDetailsNote.MaxFeatureLimitReachedNote(false)),
                onNoteAction = {},
            )
        }
    }
}

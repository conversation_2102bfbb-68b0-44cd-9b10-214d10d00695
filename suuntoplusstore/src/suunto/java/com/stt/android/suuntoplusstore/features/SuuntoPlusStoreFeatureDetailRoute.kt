package com.stt.android.suuntoplusstore.features

import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.LifecycleOwner
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.lifecycle.DisposableEffectOnLifecycleStart
import com.stt.android.compose.theme.alertColorDarker
import com.stt.android.compose.theme.confirmation
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.device.domain.WatchAndNetworkNotificationViewModel
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusDetailsNote
import com.stt.android.device.suuntoplusfeature.SuuntoPlusStoreFeatureRatingDialog
import com.stt.android.suuntoplus.SuuntoPlusItemLabel
import com.stt.android.suuntoplusstore.Item
import com.stt.android.suuntoplusstore.LibraryOperationViewState
import com.stt.android.suuntoplusstore.R
import com.stt.android.suuntoplusstore.itemdetail.SuuntoPlusStoreItemDetailScreen
import com.stt.android.suuntoplusstore.ui.SuuntoPlusStoreErrorDialog
import com.stt.android.suuntoplusstore.ui.SuuntoPlusStoreFullScreenLoading
import kotlinx.collections.immutable.toImmutableList
import com.stt.android.R as BR
import com.stt.android.device.R as DeviceR

@Composable
fun SuuntoPlusStoreFeatureDetailRoute(
    onNavigateToLibrary: (dismissDetailScreen: Boolean, isWatchface: Boolean) -> Unit,
    onShowTerms: (url: String) -> Unit,
    onReportSportsApp: (id: String, name: String?) -> Unit,
    onLearnMore: (url: String) -> Unit,
    onShareLink: (id: String) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    detailsScreenViewModel: SuuntoPlusStoreFeatureDetailScreenViewModel = hiltViewModel(),
    watchAndNetworkNotificationViewModel: WatchAndNetworkNotificationViewModel = hiltViewModel(),
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    val detailScreenViewState by detailsScreenViewModel.detailScreenViewState.collectAsState()
    val libraryOperationViewState by detailsScreenViewModel.libraryOperationViewState.collectAsState()
    val watchAndNetworkNotificationState by watchAndNetworkNotificationViewModel.watchAndNetworkNotificationState.collectAsState()
    val userRating by detailsScreenViewModel.userRating.collectAsState()

    DisposableEffectOnLifecycleStart(lifecycleOwner) { detailsScreenViewModel.refresh() }

    val scaffoldState = rememberScaffoldState()

    val toastData = libraryOperationViewState
        ?.takeUnless { watchAndNetworkNotificationState.watchSyncing }
        ?.toastData()

    LaunchedEffect(toastData) {
        val message = toastData?.message ?: return@LaunchedEffect
        watchAndNetworkNotificationViewModel.showToastMessage(
            message = message,
            actionText = toastData.actionText
        )
    }

    var showRatingDialog by rememberSaveable { mutableStateOf(false) }
    var showUninstallDialog by rememberSaveable { mutableStateOf(false) }

    val isWatchface = detailScreenViewState.data?.isWatchface ?: false

    val enableFloatingButton: Boolean
    val addToWatchButtonText: String?
    val removeFromWatchButtonText: String?
    val operationInProgress =
        libraryOperationViewState?.state == LibraryOperationViewState.State.IN_PROGRESS

    when {
        detailScreenViewState.data?.item?.inUserLibrary == false || detailScreenViewState.data?.item?.addToWatch == false -> {
            addToWatchButtonText =
                stringResource(id = DeviceR.string.suunto_plus_floating_action_button_install_on_watch)
            removeFromWatchButtonText = null
            enableFloatingButton =
                !operationInProgress && detailScreenViewState.data?.enableInstallOnWatch ?: false
        }

        detailScreenViewState.data?.item?.inUserLibrary == true && detailScreenViewState.data?.item?.addToWatch == true -> {
            addToWatchButtonText = null
            removeFromWatchButtonText =
                stringResource(id = DeviceR.string.suunto_plus_floating_action_button_uninstall_from_watch)
            enableFloatingButton = !operationInProgress
        }

        else -> {
            addToWatchButtonText = null
            removeFromWatchButtonText = null
            enableFloatingButton = false
        }
    }

    SuuntoPlusStoreItemDetailScreen(
        title = detailScreenViewState.data?.item?.name ?: "",
        showLoadingIndicator = detailScreenViewState.isLoading(),
        watchAndNetworkNotificationState = watchAndNetworkNotificationState,
        onCustomToastActionClick = {
            onNavigateToLibrary(
                false,
                detailScreenViewState.data?.item?.isWatchface ?: false
            )
        },
        enableLibraryActions = !operationInProgress,
        showAddToLibraryAction = detailScreenViewState.data?.item?.inUserLibrary == false,
        showRemoveFromLibraryAction = detailScreenViewState.data?.item?.inUserLibrary == true,
        enableFloatingButton = enableFloatingButton,
        addToWatchButtonText = addToWatchButtonText,
        removeFromWatchButtonText = removeFromWatchButtonText,
        onAddToWatch = { detailsScreenViewModel.addFeatureToWatch() },
        onAddToLibrary = { detailsScreenViewModel.addFeatureToLibrary() },
        onRemoveFromWatch = {
            detailsScreenViewModel.removeFeatureFromWatch()
        },
        onRemoveFromLibrary = {
            detailsScreenViewModel.removeFeatureFromLibrary()
        },
        showReportButton = detailScreenViewState.isLoaded() && !isWatchface,
        onReportClick = {
            val id = detailScreenViewState.data?.item?.id
            val name = detailScreenViewState.data?.item?.name
            if (id != null) {
                onReportSportsApp(id, name)
            }
        },
        onUpPressed = onDismiss,
        scaffoldState = scaffoldState,
        showShareAction = detailScreenViewState.isLoaded() && !isWatchface,
        onShareClick = {
            detailScreenViewState.data?.item?.id?.let {
                onShareLink(it)
            }
        },
        showFloatingButton = !isWatchface,
        modifier = modifier,
    ) {
        val data = detailScreenViewState.data
        when {
            detailScreenViewState is ViewState.Loaded && data != null ->
                if (!isWatchface) {
                    SuuntoPlusStoreFeatureDetails(
                        title = data.item.name,
                        description = data.item.description,
                        richDescription = detailsScreenViewModel.currentDescriptionText,
                        labels = data.item.buildLabelsList(
                            data.isCurrentWatchface,
                            data.watchStatus?.isSynced() == true
                        ),
                        bannerImageUrl = data.item.gridItemImageUrl,
                        watchPreviewImageUrl = data.item.detailScreenImageUrl,
                        learnMoreUrl = data.item.learnMoreUrl,
                        onLearnMore = onLearnMore,
                        notes = data.notes,
                        onNoteAction = { action ->
                            when (action) {
                                SuuntoPlusDetailsNote.NoteButtonAction.NAVIGATE_TO_FEATURES ->
                                    onNavigateToLibrary(false, data.item.isWatchface)
                            }
                        },
                        onTermsClick = onShowTerms,
                        modifier = Modifier.padding(bottom = MaterialTheme.spacing.xxxxlarge),
                        autoTranslate = data.item.localizedRichTextAutomatically ?: false,
                        showTranslatedText = detailsScreenViewModel.isTranslated,
                        onClickCallback = {
                            detailsScreenViewModel.changeLanguage(it)
                        },
                        showRating = detailsScreenViewModel.suuntoPlusFeedbackEnabled,
                        avgRating = data.item.avgRating,
                        ratingReviews = data.item.ratingReviews,
                        onRatingClick = {
                            showRatingDialog = true
                        },
                    )
                } else {
                    SuuntoPlusStoreWatchfaceDetails(
                        labels = data.item.buildLabelsList(
                            data.isCurrentWatchface,
                            data.watchStatus?.isSynced() == true
                        ),
                        watchPreviewImageUrl = data.item.detailScreenImageUrl,
                        title = data.item.name,
                        onSetAsCurrentWatchfaceClick = {
                            detailsScreenViewModel.setAsCurrentWatchface()
                        },
                        showSetAsCurrentWatchfaceButton = data.isWatchConnected && !data.isCurrentWatchface && data.watchStatus == SuuntoPlusPluginStatus.IN_WATCH && data.item.addToWatch == true,
                        enableButton = enableFloatingButton && !data.isSyncOngoing,
                        addToWatchButtonText = addToWatchButtonText,
                        removeFromWatchButtonText = removeFromWatchButtonText,
                        onAddToWatch = { detailsScreenViewModel.addFeatureToWatch() },
                        onRemoveFromWatch = { showUninstallDialog = true },
                        notes = data.notes,
                        onNoteAction = { action ->
                            when (action) {
                                SuuntoPlusDetailsNote.NoteButtonAction.NAVIGATE_TO_FEATURES ->
                                    onNavigateToLibrary(false, data.item.isWatchface)
                            }
                        },
                    )
                }

            detailScreenViewState.isFailure() ->
                SuuntoPlusStoreErrorDialog(
                    onDismiss = onDismiss,
                    titleRes = (detailScreenViewState as? ViewState.Error)?.errorEvent?.errorStringRes,
                    onRetry = { detailsScreenViewModel.refresh() },
                )
        }
    }

    when (libraryOperationViewState?.state) {
        LibraryOperationViewState.State.IN_PROGRESS -> SuuntoPlusStoreFullScreenLoading()
        LibraryOperationViewState.State.ERROR -> {
            SuuntoPlusStoreErrorDialog(
                onDismiss = { detailsScreenViewModel.clearLibraryOperationState() },
                titleRes = libraryOperationViewState?.errorEvent?.errorStringRes
            )
        }

        else -> {}
    }

    if (showRatingDialog) {
        SuuntoPlusStoreFeatureRatingDialog(
            initialRating = userRating?.rating,
            initialComment = userRating?.comment,
            onDismiss = { showRatingDialog = false },
            onSubmit = { rating, comment ->
                showRatingDialog = false
                detailsScreenViewModel.rateFeature(rating, comment)
            },
        )
    }

    if (showUninstallDialog) {
        ConfirmationDialog(
            text = buildAnnotatedString {
                withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                    append(stringResource(id = DeviceR.string.suunto_plus_uninstall_sports_app_confirmation_title))
                }
            },
            cancelButtonText = stringResource(id = BR.string.cancel),
            confirmButtonText = stringResource(id = DeviceR.string.suunto_plus_uninstall_sports_app_confirmation_button),
            onDismissRequest = {
                showUninstallDialog = false
            },
            onConfirm = {
                showUninstallDialog = false
                detailsScreenViewModel.removeFeatureFromWatch()
            },
            useDestructiveColorForConfirm = true,
        )
    }
}

private data class LibraryOperationToast(
    val message: String?,
    val actionText: String?
)

@Composable
private fun LibraryOperationViewState.toastData(): LibraryOperationToast {
    val message: String?
    val actionText: String?
    when (state) {
        LibraryOperationViewState.State.SUCCESS -> {
            message = when (operation) {
                LibraryOperationViewState.Operation.ADD_TO_WATCH ->
                    stringResource(
                        DeviceR.string.suunto_plus_guide_or_sports_app_added_to_watch,
                        itemName
                    )

                LibraryOperationViewState.Operation.REMOVE_FROM_WATCH ->
                    stringResource(
                        DeviceR.string.suunto_plus_guide_or_sports_app_removed_from_watch,
                        itemName
                    )

                LibraryOperationViewState.Operation.ADD_TO_LIBRARY -> stringResource(
                    id = if (isWatchface) {
                        R.string.suunto_plus_store_item_detail_screen_saved_to_my_watch_faces_label
                    } else {
                        R.string.suunto_plus_store_sports_app_added_to_my_apps
                    }
                )

                LibraryOperationViewState.Operation.REMOVE_FROM_LIBRARY -> stringResource(
                    id = if (isWatchface) {
                        R.string.suunto_plus_store_sports_app_removed_from_my_watch_faces
                    } else {
                        R.string.suunto_plus_store_sports_app_removed_from_my_apps
                    }
                )
            }

            actionText = if (operation != LibraryOperationViewState.Operation.REMOVE_FROM_WATCH) {
                stringResource(id = R.string.suunto_plus_store_view_my_library_snackbar_action)
            } else {
                null
            }
        }

        else -> {
            message = null
            actionText = null
        }
    }

    return LibraryOperationToast(message = message, actionText = actionText)
}

@Composable
private fun Item.Feature.buildLabelsList(isCurrentWatchface: Boolean, isSynced: Boolean) =
    buildList {
        if (inUserLibrary == true) {
            if (isWatchface) {
                if (isCurrentWatchface) {
                    add(
                        SuuntoPlusItemLabel(
                            text = stringResource(id = com.stt.android.device.R.string.suunto_plus_store_watch_faces_in_use_label),
                            color = MaterialTheme.colors.confirmation
                        )
                    )
                }
                add(
                    SuuntoPlusItemLabel(
                        text = stringResource(id = com.stt.android.device.R.string.suunto_plus_store_watch_faces_in_my_watch_faces_label),
                        color = MaterialTheme.colors.primary
                    )
                )
            } else {
                add(
                    SuuntoPlusItemLabel(
                        text = stringResource(id = DeviceR.string.suunto_plus_store_sports_app_in_my_apps_label),
                        color = MaterialTheme.colors.primary
                    )
                )
            }

            if (isSynced) {
                add(
                    SuuntoPlusItemLabel(
                        text = stringResource(id = com.stt.android.device.R.string.suunto_plus_status_synced_to_watch),
                        color = MaterialTheme.colors.confirmation
                    )
                )
            }
        }

        addAll(
            labels.map {
                SuuntoPlusItemLabel(
                    text = it,
                    color = MaterialTheme.colors.alertColorDarker
                )
            }
        )

        if (!isWatchface) {
            add(SuuntoPlusItemLabel(stringResource(id = R.string.suunto_plus_store_feature_detail_screen_type)))
        }
        addAll(categoryTitles.map { SuuntoPlusItemLabel(it) })
    }.toImmutableList()

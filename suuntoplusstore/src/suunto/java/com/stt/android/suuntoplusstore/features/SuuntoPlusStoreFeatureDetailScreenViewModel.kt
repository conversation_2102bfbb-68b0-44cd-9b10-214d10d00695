package com.stt.android.suuntoplusstore.features

import android.content.SharedPreferences
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.domain.GetCurrentWatchfaceIdUseCase
import com.stt.android.device.domain.GetWatchCapabilitiesUseCase
import com.stt.android.device.domain.SetCurrentWatchfaceUseCase
import com.stt.android.device.domain.suuntoplusfeature.GetSuuntoPlusFeatureUseCase
import com.stt.android.device.domain.suuntoplusfeature.NumberOfEnabledFeaturesUseCase
import com.stt.android.device.domain.suuntoplusfeature.RemoveFeatureFromLibraryUseCase
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusStoreUserRating
import com.stt.android.device.domain.suuntoplusguide.IsSuuntoPlusGuideSyncOngoingUseCase
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTrigger
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTriggerImpl
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusStoreWatchCompatibility.Incompatible
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusDetailsNote
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.suuntoplusstore.Item
import com.stt.android.suuntoplusstore.LibraryOperationViewState
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations
import com.stt.android.suuntoplusstore.analytics.SuuntoPlusStoreAnalytics
import com.stt.android.suuntoplusstore.features.domain.usecases.AddSuuntoPlusStoreFeatureToLibraryUseCase
import com.stt.android.suuntoplusstore.features.domain.usecases.FetchSuuntoPlusStoreSingleFeatureUseCase
import com.stt.android.device.domain.FetchSuuntoPlusStoreFeatureRatingUseCase
import com.stt.android.device.domain.RateSuuntoPlusStoreFeatureUseCase
import com.stt.android.suuntoplusstore.itemdetail.SuuntoPluStoreLibraryOperationRunnerImpl
import com.stt.android.suuntoplusstore.itemdetail.SuuntoPlusStoreFeatureDetailScreenViewState
import com.stt.android.suuntoplusstore.itemdetail.SuuntoPlusStoreLibraryOperationRunner
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreFeatureId
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK_DEFAULT
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR
import com.stt.android.device.R as DeviceR

@HiltViewModel
class SuuntoPlusStoreFeatureDetailScreenViewModel @Inject constructor(
    coroutinesDispatchers: CoroutinesDispatchers = CoroutinesDispatcherProvider(),
    private val fetchSuuntoPlusStoreSingleFeatureUseCase: FetchSuuntoPlusStoreSingleFeatureUseCase,
    private val addFeatureToLibraryUseCase: AddSuuntoPlusStoreFeatureToLibraryUseCase,
    private val removeFeatureFromLibraryUseCase: RemoveFeatureFromLibraryUseCase,
    private val limitNumberOfEnabledFeaturesUseCase: NumberOfEnabledFeaturesUseCase,
    private val currentWatchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
    private val analytics: SuuntoPlusStoreAnalytics,
    remoteSyncJobLauncher: SuuntoPlusGuideRemoteSyncJobLauncher,
    savedStateHandle: SavedStateHandle,
    private val getCurrentWatchfaceIdUseCase: GetCurrentWatchfaceIdUseCase,
    private val setCurrentWatchfaceUseCase: SetCurrentWatchfaceUseCase,
    private val getSuuntoPlusFeatureUseCase: GetSuuntoPlusFeatureUseCase,
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val isSyncOngoingUseCase: IsSuuntoPlusGuideSyncOngoingUseCase,
    private val rateFeatureUseCase: RateSuuntoPlusStoreFeatureUseCase,
    private val fetchFeatureRatingUseCase: FetchSuuntoPlusStoreFeatureRatingUseCase,
    @param:FeatureTogglePreferences private val featureTogglePrefs: SharedPreferences,
) : CoroutineViewModel(coroutinesDispatchers),
    DelayedSuuntoPlusGuideRemoteSyncTrigger by DelayedSuuntoPlusGuideRemoteSyncTriggerImpl(
        remoteSyncJobLauncher
    ),
    SuuntoPlusStoreLibraryOperationRunner by SuuntoPluStoreLibraryOperationRunnerImpl(analytics) {

    private val featureId: String =
        checkNotNull(savedStateHandle[SuuntoPlusStoreDestinations.FEATURE_DETAIL_ID_KEY])

    private val _detailScreenViewState =
        MutableStateFlow<ViewState<SuuntoPlusStoreFeatureDetailScreenViewState>>(loading())
    val detailScreenViewState: StateFlow<ViewState<SuuntoPlusStoreFeatureDetailScreenViewState>>
        get() = _detailScreenViewState

    var currentDescriptionText by mutableStateOf<String?>(null)
        private set

    private var fetchJob: Job? = null

    var isTranslated by mutableStateOf(false)

    val suuntoPlusFeedbackEnabled: Boolean by lazy {
        featureTogglePrefs.getBoolean(
            KEY_ENABLE_SUUNTO_PLUS_FEEDBACK,
            KEY_ENABLE_SUUNTO_PLUS_FEEDBACK_DEFAULT,
        )
    }

    private val _userRating = MutableStateFlow<SuuntoPlusStoreUserRating?>(null)
    val userRating: StateFlow<SuuntoPlusStoreUserRating?>
        get() = _userRating

    init {
        fetchUserRating()
    }

    fun refresh() {
        fetchFeature()
    }

    fun addFeatureToWatch() {
        _detailScreenViewState.value.data?.item?.let {
            addFeatureToWatch(it)
        } ?: Timber.w("Cannot add feature to watch: Item is null")
    }

    private fun addFeatureToWatch(feature: Item.Feature) {
        launchLibraryOperation(
            item = feature,
            operation = LibraryOperationViewState.Operation.ADD_TO_WATCH,
            onComplete = { refresh() }
        ) {
            addFeatureToLibraryUseCase.addFeatureToWatch(feature)
            syncNow(alwaysTriggerWatchSync = true)
        }
    }

    fun addFeatureToLibrary() {
        _detailScreenViewState.value.data?.item?.let {
            addFeatureToLibrary(it)
        } ?: Timber.w("Cannot add feature to library: Item is null")
    }

    private fun addFeatureToLibrary(feature: Item.Feature) {
        launchLibraryOperation(
            item = feature,
            operation = LibraryOperationViewState.Operation.ADD_TO_LIBRARY,
            onComplete = { refresh() }
        ) {
            addFeatureToLibraryUseCase.addFeatureToLibrary(feature)
            syncNow(alwaysTriggerWatchSync = false)
        }
    }

    fun changeLanguage(translated: Boolean) {
        isTranslated = translated
        currentDescriptionText = if (translated) {
            _detailScreenViewState.value.data?.item?.localizedRichText ?: ""
        } else {
            _detailScreenViewState.value.data?.item?.richDescription ?: ""
        }
    }

    fun removeFeatureFromLibrary() {
        _detailScreenViewState.value.data?.item?.let {
            removeFeatureFromLibrary(it)
        } ?: Timber.w("Cannot remove feature from library: Item is null")
    }

    private fun removeFeatureFromLibrary(feature: Item) {
        launchLibraryOperation(
            item = feature,
            operation = LibraryOperationViewState.Operation.REMOVE_FROM_LIBRARY,
            onComplete = { refresh() }
        ) {
            removeFeatureFromLibraryUseCase.removeFeatureFromLibrary(feature.id)
            syncNow(alwaysTriggerWatchSync = true)
        }
    }

    fun removeFeatureFromWatch() {
        _detailScreenViewState.value.data?.item?.let {
            removeFeatureFromWatch(it)
        } ?: Timber.w("Cannot remove feature from watch: Item is null")
    }

    private fun removeFeatureFromWatch(feature: Item) {
        launchLibraryOperation(
            item = feature,
            operation = LibraryOperationViewState.Operation.REMOVE_FROM_WATCH,
            onComplete = { refresh() }
        ) {
            removeFeatureFromLibraryUseCase.removeFeatureFromWatch(feature.id)
            syncNow(alwaysTriggerWatchSync = true)
        }
    }

    private fun fetchUserRating() {
        launch {
            runSuspendCatching {
                fetchFeatureRatingUseCase.fetchRating(featureId)
            }.onSuccess {
                _userRating.tryEmit(it)
            }
        }
    }

    fun rateFeature(rating: Int, comment: String) {
        _userRating.tryEmit(SuuntoPlusStoreUserRating(rating, comment))
        launch {
            runSuspendCatching {
                rateFeatureUseCase.rateFeature(featureId, rating, comment)
            }.onSuccess { success ->
                if (success) {
                    fetchFeature()
                }
            }
        }
    }

    private fun fetchFeature() {
        fetchJob?.cancel()
        fetchJob = launch {
            val existingData = _detailScreenViewState.value.data
            runSuspendCatching {
                val (_, watchCapabilities) = currentWatchCapabilitiesUseCase.getCurrentCapabilities()
                val feature = fetchSuuntoPlusStoreSingleFeatureUseCase.fetchFeature(
                    id = SuuntoPlusStoreFeatureId(featureId),
                    capabilities = watchCapabilities ?: SuuntoWatchCapabilities.EMPTY
                )

                val areSuuntoPlusFeaturesSupported =
                    watchCapabilities?.areSuuntoPlusFeaturesSupported == true

                if (feature != null) {
                    val isWatchface = feature.isWatchface()
                    val isSpaceForMoreFeatures = limitNumberOfEnabledFeaturesUseCase
                        .canOneMoreFeatureBeEnabled(isWatchface)

                    val viewStateData = SuuntoPlusStoreFeatureDetailScreenViewState(
                        item = feature.toViewState(),
                        // TODO: 13.7.2022 Check the note priorities and if multiple notes
                        // should be shown
                        notes = when {
                            feature.watchCompatibility is Incompatible -> persistentListOf(
                                SuuntoPlusDetailsNote.GenericTextNote(
                                    text = feature.watchCompatibility.incompatibilityNote
                                ),
                            )

                            watchCapabilities?.areSuuntoPlusFeaturesSupported == false -> persistentListOf(
                                SuuntoPlusDetailsNote.GenericTextResourceNote(
                                    DeviceR.string.suunto_plus_watch_not_compatible_with_sports_app
                                ),
                            )

                            feature.addToWatch == false && areSuuntoPlusFeaturesSupported && !isSpaceForMoreFeatures -> {
                                persistentListOf(
                                    SuuntoPlusDetailsNote.MaxFeatureLimitReachedNote(
                                        isWatchface
                                    )
                                )
                            }

                            else -> persistentListOf()
                        },
                        enableInstallOnWatch = isSpaceForMoreFeatures,
                        isWatchface = isWatchface,
                        isCurrentWatchface = false,
                        watchfaceId = feature.id,
                        watchStatus = null,
                        isWatchConnected = false,
                        isSyncOngoing = false,
                    )

                    combine(
                        getSuuntoPlusFeatureUseCase.getWatchStateById(feature.id),
                        getCurrentWatchfaceIdUseCase(),
                        isWatchConnectedUseCase.invoke(),
                        isSyncOngoingUseCase.getSyncStateFlow(),
                    ) { watchState, currentWatchfaceId, isWatchConnected, syncOngoingStatus ->
                        viewStateData.copy(
                            isCurrentWatchface = feature.id == currentWatchfaceId,
                            watchStatus = watchState,
                            isWatchConnected = isWatchConnected,
                            isSyncOngoing = syncOngoingStatus.isSyncOngoing
                        )
                    }.distinctUntilChanged()
                        .collect { setDetailScreenViewState(loaded(it)) }
                } else {
                    setDetailScreenViewState(
                        failure(
                            errorEvent = ErrorEvent(
                                shouldHandle = true,
                                errorStringRes = BaseR.string.error_generic,
                                canRetry = false,
                            ),
                            data = existingData
                        )
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "When fetching SuuntoPlus Store view state for feature $featureId: ")
                setDetailScreenViewState(failure(ErrorEvent.get(e::class), existingData))
            }
        }
    }

    private fun setDetailScreenViewState(viewState: ViewState<SuuntoPlusStoreFeatureDetailScreenViewState>) {
        _detailScreenViewState.value = viewState
        if (currentDescriptionText.isNullOrEmpty()) { // It‘s only used for initialization and doesn’t change the display state
            _detailScreenViewState.value.data?.item?.let {
                if (!it.localizedRichText.isNullOrEmpty()) {
                    currentDescriptionText = it.localizedRichText
                    isTranslated = true
                } else {
                    currentDescriptionText = it.richDescription
                }
            }
        }
    }

    fun setAsCurrentWatchface() {
        launch {
            _detailScreenViewState.value.data?.watchfaceId?.let {
                setCurrentWatchfaceUseCase(it)
            }
        }
    }

    override fun onCleared() {
        fetchJob?.cancel()
        super.onCleared()
    }
}

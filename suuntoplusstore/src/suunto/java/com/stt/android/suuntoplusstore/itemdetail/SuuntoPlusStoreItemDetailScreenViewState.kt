package com.stt.android.suuntoplusstore.itemdetail

import androidx.compose.runtime.Immutable
import com.stt.android.SimGuideMessagesFormatter
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusDetailsNote
import com.stt.android.device.suuntoplusguide.details.WorkoutDay
import com.stt.android.suuntoplusstore.Item
import com.stt.android.device.remote.suuntoplusguide.TrainingPlanWeek
import com.stt.android.device.suuntoplusguide.details.WorkoutItem
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Immutable
data class SuuntoPlusStoreFeatureDetailScreenViewState(
    val item: Item.Feature,
    val notes: ImmutableList<SuuntoPlusDetailsNote> = persistentListOf(),
    val enableInstallOnWatch: Boolean,
    val isWatchface: Boolean,
    val isCurrentWatchface: <PERSON>ole<PERSON>,
    val watchfaceId: String,
    val watchStatus: SuuntoPlusPluginStatus?,
    val isWatchConnected: Boolean,
    val isSyncOngoing: Boolean,
)

@Immutable
data class SuuntoPlusStoreGuideDetailScreenViewState(
    val item: Item.Guide,
    val backgroundUrl: String?,
    val notes: ImmutableList<SuuntoPlusDetailsNote> = persistentListOf(),
    val showShareAction: Boolean,
    val workoutItem: WorkoutItem?,
    val formatter: SimGuideMessagesFormatter,
    val notSupported: Boolean
)

@Immutable
data class PlanDetailScreenViewState(
    val item: Item.Guide,
    val backgroundUrl: String?,
    val weeks: ImmutableList<TrainingPlanWeek>,
    val notes: ImmutableList<SuuntoPlusDetailsNote> = persistentListOf(),
    val fileModificationTime: Long?,
    val showShareAction: Boolean,
    val isRunDevice: Boolean,
    val notSupported: Boolean
)

@Immutable
data class PlanDayScreenViewState(
    val workoutDay: WorkoutDay,
    val showShareAction: Boolean?,
    val shareId: String?,
)

@Immutable
data class RestDayScreenViewState(
    val dayNumber: Int,
    val showShareAction: Boolean?,
    val shareId: String?,
)

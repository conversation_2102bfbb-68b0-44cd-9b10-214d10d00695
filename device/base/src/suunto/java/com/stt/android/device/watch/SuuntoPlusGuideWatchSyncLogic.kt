package com.stt.android.device.watch

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncAlreadyRunningException
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogEventDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogUtil
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogUtilImpl
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateRepository
import com.stt.android.device.datasource.SuuntoPlusPluginDeviceStatus
import com.stt.android.device.datasource.WatchPluginStatusDataSource
import com.stt.android.device.datasource.suuntoplusfeature.SuuntoPlusFeaturesLocalDataSource
import com.stt.android.device.datasource.suuntoplusguide.GuideZAPPFileStorage
import com.stt.android.device.datasource.suuntoplusguide.SuuntoPlusGuidesLocalDataSource
import com.stt.android.device.datasource.suuntoplusguide.TrainingPlansLocalDataSource
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeature
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuide
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginType
import com.stt.android.device.domain.suuntoplusguide.TrainingPlan
import com.stt.android.device.domain.suuntoplusguide.TrainingPlanId
import com.stt.android.device.domain.suuntoplusguide.guideSorted
import com.stt.android.device.domain.suuntoplusguide.planSorted
import com.stt.android.device.domain.suuntoplusguide.pluginId
import com.stt.android.exceptions.device.WatchPluginStorageFullException
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import com.suunto.connectivity.suuntoplusguide.SuuntoPlusGuideSyncLogicResult
import com.suunto.connectivity.suuntoplusguide.SuuntoPlusGuideWatchSyncTrigger
import com.suunto.connectivity.suuntoplusguide.WatchBusyStateProvider
import com.suunto.connectivity.sync.WatchBusyException
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.FileNotFoundException
import java.text.Collator
import java.util.Locale
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import kotlin.coroutines.cancellation.CancellationException

class SuuntoPlusGuideWatchSyncLogic
@Inject constructor(
    private val watchDataSource: SuuntoPlusWatchPluginDataSource,
    private val localTrainingPlanDataSource: TrainingPlansLocalDataSource,
    private val localGuidesDataSource: SuuntoPlusGuidesLocalDataSource,
    private val localFeaturesDataSource: SuuntoPlusFeaturesLocalDataSource,
    private val pluginStatusDataSource: WatchPluginStatusDataSource,
    private val zappFileStorage: GuideZAPPFileStorage,
    private val syncLogEventDao: SuuntoPlusGuideSyncLogEventDao,
    private val syncStateRepository: SuuntoPlusSyncStateRepository,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : SuuntoPlusGuideWatchSyncTrigger,
    SuuntoPlusGuideSyncLogUtil by SuuntoPlusGuideSyncLogUtilImpl(syncLogEventDao) {

    private val collator = Collator.getInstance(Locale.getDefault())

    private data class PluginsInWatch(
        val guides: List<WatchPluginInfo> = emptyList(),
        val features: List<WatchPluginInfo> = emptyList(),
        val plans: Map<TrainingPlanId, List<WatchPluginInfo>> = emptyMap(),
    ) {
        val current: List<WatchPluginInfo>
            get() = guides + features + plans.values.flatten()

        override fun toString(): String {
            return "Guides[${guides.joinToString { it.id }}], " +
                "Features[${features.joinToString { it.id }}], " +
                "Plans[${
                    plans.toList()
                        .joinToString { (planId, guides) -> planId.id + ':' + guides.joinToString { it.id } }
                }]"
        }
    }

    private class InternalSyncState(
        val serial: String,
        val capabilities: SuuntoWatchCapabilities,
        val errorsLogs: MutableList<String> = mutableListOf(),
        val watchWasUpdated: AtomicBoolean = AtomicBoolean(false),
        val triggerBackendSync: AtomicBoolean = AtomicBoolean(false),
        var localGuidesById: Map<SuuntoPlusGuideId, SuuntoPlusGuide> = emptyMap(),
        var localPlansById: Map<TrainingPlanId, TrainingPlan> = emptyMap(),
        var sortedGuides: List<SuuntoPlusGuide> = emptyList(),
        var sortedPlans: List<TrainingPlan> = emptyList(),
        var localFeaturesByPluginId: Map<String, SuuntoPlusFeature> = emptyMap(),
        var pluginsInWatch: PluginsInWatch = PluginsInWatch(),
        var currentWatchfaceId: String? = null,
    ) {
        val currentGuidesInWatch: List<WatchPluginInfo>
            get() = pluginsInWatch.guides

        val currentPlansInWatch: Map<TrainingPlanId, List<WatchPluginInfo>>
            get() = pluginsInWatch.plans

        val currentFeaturesInWatch: List<WatchPluginInfo>
            get() = pluginsInWatch.features
    }

    override suspend fun syncWatchPlugins(
        serial: String,
        capabilities: SuuntoWatchCapabilities,
        watchBusyStateProvider: WatchBusyStateProvider
    ): SuuntoPlusGuideSyncLogicResult = try {
        syncWatchPluginsInternal(serial, capabilities, watchBusyStateProvider)
    } catch (e: Exception) {
        Timber.w(e, "Failed to sync watch plugins.")
        withContext(NonCancellable) {
            syncStateRepository.ensureIdle(serial)
        }
        val result = processSyncErrorsAndStopSync(listOf(e.message.orEmpty()))
        if (e is CancellationException) throw e else result
    }

    private suspend fun processSyncErrorsAndStopSync(errorsLogs: List<String>) =
        processSyncErrorsAndStopSync(
            hasNewData = false,
            errorsLogs = errorsLogs,
            isWatchSync = true,
            triggerBackendSync = false,
            triggerWatchSync = false,
        )

    private suspend fun syncWatchPluginsInternal(
        serial: String,
        capabilities: SuuntoWatchCapabilities,
        watchBusyStateProvider: WatchBusyStateProvider
    ): SuuntoPlusGuideSyncLogicResult = with(InternalSyncState(serial, capabilities)) {
        // Keep internal sync state using a helper class to avoid large amounts of local
        // variables and passing lots of arguments

        // Sync features if watch has 'feat_plugins_1' capability
        var syncFeatureSelection = capabilities.areSuuntoPlusFeaturesSupported

        // Sync guides if watch has both 'feat_plugins_1' and 'feat_guides_v1' capabilities
        val syncGuides = capabilities.areSuuntoPlusGuidesSupported

        // We can't do anything with the default watch face, at least for now
        if (capabilities.areSuuntoPlusWatchfaceSupported) {
            currentWatchfaceId = watchDataSource.getDefaultWatchfaceId(serial)
        }

        runSuspendCatching {
            if (!syncFeatureSelection && !syncGuides) {
                Timber.d("No watch support. Skip sync.")
                return processSyncErrorsAndStopSync(
                    listOf("Not supported: feat_plugins_1 capability missing for $serial")
                )
            }

            val previousCapabilities = syncStateRepository.getPreviousSyncCapabilities(serial)
            when {
                previousCapabilities?.remoteSyncCapabilities == null -> {
                    Timber.d("Remote sync has never succeeded. Skip watch sync.")
                    // Update initial status for plug-ins in watch even before the remote sync has ever
                    // run. This makes sure the UI is immediately up-do-date after the remote sync runs.
                    updateInitialWatchPluginStatuses(serial, capabilities)
                    return SuuntoPlusGuideSyncLogicResult.Success(triggerBackendSync = true)
                }

                previousCapabilities.remoteSyncCapabilities != capabilities -> {
                    Timber.d("Remote sync was run with different capabilities. Skip watch sync.")
                    return SuuntoPlusGuideSyncLogicResult.Success(triggerBackendSync = true)
                }

                previousCapabilities.watchSyncCapabilities != null &&
                    previousCapabilities.watchSyncCapabilities != capabilities -> {
                    Timber.d("Watch capabilities have changed since last watch sync.")
                    // TODO: should probably try to preserve settings data
                    // TODO: see https://suunto.tpondemand.com/entity/139627-sports-apps-settings-lost-if-watch
                }

                else -> { /* All good, capabilities have not changed. Proceed with normal sync */
                }
            }

            watchBusyStateProvider.throwIfBusy()

            // Make sure remote sync is not running and log sync start event
            // Throws if already syncing
            syncStateRepository.ensureSyncStart(serial, SuuntoPlusSyncState.WATCH_SYNC_ONGOING)
            syncLogEventDao.logSyncStart(isWatchSync = true)

            if (syncFeatureSelection && !localFeaturesDataSource.havePluginIds()) {
                Timber.d("Skipping SuuntoPlus™ feature sync until next remote sync due to missing plug-in IDs")
                syncFeatureSelection = false
            }

            // Loads current guides from local DB & watch
            loadCurrentGuides(capabilities)

            // Delete removed plugins from latest backend sync
            if (syncGuides) {
                deleteGuidesFromWatch()
                deleteTrainingPlanGuidesFromWatch()
            }

            watchBusyStateProvider.throwIfBusy()

            if (syncFeatureSelection) {
                deleteFeaturesFromWatch()
            }

            // Sort working list of guides by priority
            sortedGuides = localGuidesById.values.toList().guideSorted()
            sortedPlans = localPlansById.values.toList().planSorted()

            // Install missing Features to watch
            if (syncFeatureSelection) {
                installFeaturesToWatch(watchBusyStateProvider)
            }

            // Push guides to watch in order and update pending sync flags
            if (syncGuides) {
                installGuidesToWatch(watchBusyStateProvider)
                installPlansToWatch(watchBusyStateProvider)
            }
        }.onFailure { e ->
            if (e is SuuntoPlusGuideSyncAlreadyRunningException) {
                Timber.d("Sync already ongoing. Returning failure without updating sync log")
                return SuuntoPlusGuideSyncLogicResult.Failure("Sync already ongoing")
            }

            val message = "SuuntoPlus guides watch sync failed"
            Timber.w(e, message)
            errorsLogs.add("$message. ${e::class.java.name}: ${e.message}")
        }

        // Update device status for all guides in database. The flag is updated even when there
        // are sync errors.
        runSuspendCatching {
            updateLocalDBAfterWatchSync(
                syncGuides = syncGuides,
                syncFeatureSelection = syncFeatureSelection,
            )
        }.onFailure { e ->
            val message = "Failed to update local guide status"
            Timber.w(e, message)
            errorsLogs.add("$message. ${e::class.java.name}: ${e.message}")
        }

        withContext(NonCancellable) {
            runSuspendCatching {
                if (errorsLogs.isEmpty()) {
                    syncStateRepository.updateWatchSyncCapabilities(serial, capabilities)
                }
                syncStateRepository.ensureIdle(serial)
            }.onFailure { e ->
                val message = "Failed to update sync state to IDLE"
                Timber.w(e, message)
                errorsLogs.add("$message. ${e::class.java.name}: ${e.message}")
            }
        }

        return processSyncErrorsAndStopSync(
            errorsLogs = errorsLogs,
            hasNewData = watchWasUpdated.get(),
            isWatchSync = true,
            triggerBackendSync = triggerBackendSync.get(),
            triggerWatchSync = false
        )
    }

    private suspend fun InternalSyncState.updateLocalDBAfterWatchSync(
        syncGuides: Boolean,
        syncFeatureSelection: Boolean,
    ) = withContext(coroutinesDispatchers.io) {
        val refreshWatchPluginList = errorsLogs.isNotEmpty() || watchWasUpdated.get()
        val pluginsAfterSync = if (refreshWatchPluginList) {
            // Either watch was updated or an error occurred. Refresh list from watch to know
            // where we are at.
            fetchPluginsFromWatch(serial).current
        } else {
            // Nothing has changed and no errors - our current list if up-to-date
            pluginsInWatch.current
        }

        pluginsAfterSync.mapNotNull { plugin ->
            when {
                syncGuides && plugin.isGuide && !plugin.isStatic -> {
                    if (localGuidesById.containsKey(SuuntoPlusGuideId(plugin.id)) || localPlansById.allGuideIds()
                            .contains(plugin.id)
                    ) {
                        SuuntoPlusPluginDeviceStatus(
                            watchSerial = serial,
                            pluginId = plugin.id,
                            modifiedMillis = plugin.modificationTimeSeconds * 1000L,
                            capabilities = capabilities,
                            status = SuuntoPlusPluginStatus.IN_WATCH,
                            interestValue = plugin.interestValue,
                            type = SuuntoPlusPluginType.GUIDE,
                        )
                    } else {
                        Timber.d("Unknown guide in watch: $plugin")
                        null
                    }
                }

                syncFeatureSelection && plugin.isFeatureOrDevice -> {
                    if (localFeaturesByPluginId.containsKey(plugin.id)) {
                        SuuntoPlusPluginDeviceStatus(
                            watchSerial = serial,
                            pluginId = plugin.id,
                            modifiedMillis = plugin.modificationTimeSeconds * 1000L,
                            capabilities = capabilities,
                            status = SuuntoPlusPluginStatus.IN_WATCH,
                            interestValue = plugin.interestValue,
                            type = SuuntoPlusPluginType.FEATURE,
                        )
                    } else {
                        Timber.d("Unknown feature in watch: $plugin")
                        null
                    }
                }

                else -> null
            }
        }.takeUnless(List<*>::isEmpty)
            ?.let { pluginStatusDataSource.updateWatchStatus(it) }

        val pluginIdsAfterSync = pluginsAfterSync.map(WatchPluginInfo::id).toSet()
        val featureIdsToUpdateAsync = async {
            loadFeatureIdsToUpdate(pluginIdsAfterSync, syncFeatureSelection)
        }
        val guideIdsToUpdate = loadGuideIdsToUpdate(pluginIdsAfterSync, syncGuides)
        (featureIdsToUpdateAsync.await() + guideIdsToUpdate)
            .takeUnless(List<*>::isEmpty)
            ?.let { pluginIds ->
                pluginStatusDataSource.updateWatchStatus(
                    watchSerial = serial,
                    pluginIds = pluginIds,
                    status = SuuntoPlusPluginStatus.UNKNOWN,
                )
            }
    }

    private suspend fun InternalSyncState.loadFeatureIdsToUpdate(
        pluginIdsAfterSync: Set<String>,
        syncFeatureSelection: Boolean,
    ): List<String> = if (syncFeatureSelection) {
        // If a feature has IN_WATCH/INSTALLING status, but is not listed as being installed
        // by the watch, set status to UNKNOWN
        localFeaturesDataSource.listSuuntoPlusFeaturesInGivenStates(
            watchSerial = serial,
            states = setOf(
                SuuntoPlusPluginStatus.IN_WATCH,
                SuuntoPlusPluginStatus.INSTALLING,
            )
        ).mapNotNull { feature ->
            (feature.pluginId ?: feature.id)
                .takeUnless(pluginIdsAfterSync::contains)
                ?.also {
                    Timber.d("Feature ID ${feature.id} (plug-in ID ${feature.pluginId}) no longer available in watch")
                }
        }
    } else {
        emptyList()
    }

    private suspend fun InternalSyncState.loadGuideIdsToUpdate(
        pluginIdsAfterSync: Set<String>,
        syncGuides: Boolean,
    ): List<String> = if (syncGuides) {
        // If a guide has IN_WATCH/INSTALLING status, but is not listed as being installed
        // by the watch, set status to UNKNOWN
        localGuidesDataSource.listSuuntoPlusGuidesInGivenStates(
            watchSerial = serial,
            states = setOf(
                SuuntoPlusPluginStatus.IN_WATCH,
                SuuntoPlusPluginStatus.INSTALLING
            )
        ).mapNotNull { guide ->
            guide.pluginId
                .takeUnless(pluginIdsAfterSync::contains)
                ?.also {
                    Timber.d("Guide ID ${guide.id} no longer available in watch")
                }
        }
    } else {
        emptyList()
    }

    private suspend fun InternalSyncState.fetchPluginsFromWatch(serial: String): PluginsInWatch {
        val plugins = watchDataSource.listPlugins(serial)
        val plans = localPlansById.mapValues { (_, plan) ->
            plugins.filter { plan.allGuideIds().contains(it.id) }
        }.filterValues { it.isNotEmpty() }
        val features = plugins.filter { it.isFeatureOrDevice } // both features and devices
        val guides = (plugins - plans.values.flatten().toSet()).filter { plugin ->
            plugin.isGuide &&
                // Production ESW releases have no static Guides, but unofficial release may have
                // for debugging purposes. Those cannot be uninstalled so ignore them for now.
                !plugin.isStatic
        }
        return PluginsInWatch(guides = guides, features = features, plans = plans)
    }

    private fun TrainingPlan.allGuideIds(): List<String> = courses?.values?.flatten().orEmpty()

    private fun Map<TrainingPlanId, TrainingPlan>.allGuideIds() =
        values.map { it.allGuideIds() }.flatten()

    /**
     * Loads current guides from local DB & watch, and stores them to [InternalSyncState].
     */
    private suspend fun InternalSyncState.loadCurrentGuides(
        capabilities: SuuntoWatchCapabilities,
    ) = withContext(coroutinesDispatchers.io) {
        // Get current guides from local DB and watch
        val listLocalGuidesAsync = async { listLocalGuides() }
        val listLocalPlansAsync = async { listLocalPlans() }
        val listLocalFeaturesAsync = async { listLocalFeatures() }
        localFeaturesByPluginId = listLocalFeaturesAsync.await()
        localPlansById = listLocalPlansAsync.await()
        val planGuideIds = localPlansById.allGuideIds()
        localGuidesById = listLocalGuidesAsync.await().filterKeys { !planGuideIds.contains(it.id) }

        pluginsInWatch = fetchPluginsFromWatch(serial)

        val maxNumberOfEnabledFeatures = capabilities.maxNumberOfEnabledFeatures
        val maxNumberOfEnabledWatchFaces = capabilities.maxNumberOfEnabledWatchface
        val localWatchFacesByPluginId = localFeaturesByPluginId.filter { it.value.isWatchface() }
        // Can happen if user switched from Orca/Race to Sparrow(max features = 15)
        if (currentFeaturesInWatch.size > (maxNumberOfEnabledFeatures + maxNumberOfEnabledWatchFaces) ||
            (localFeaturesByPluginId - localWatchFacesByPluginId.keys).size > maxNumberOfEnabledFeatures ||
            localWatchFacesByPluginId.size > maxNumberOfEnabledWatchFaces
        ) {
            // Update enabled state for extra features in DB
            disableExtraFeatures(maxNumberOfEnabledFeatures, maxNumberOfEnabledWatchFaces)
            // Get latest data from DB
            localFeaturesByPluginId = listLocalFeatures()
        }

        Timber.d("Local Guides: ${localGuidesById.values.joinToString { "${it.id}: ${it.name}" }}")
        Timber.d(
            "Local Plan with Guides in plan: ${
                localPlansById.values.joinToString { plan ->
                    "${plan.id}: ${
                        plan.courses?.values?.flatten()?.joinToString { it }
                    }"
                }
            }"
        )
        Timber.d("Local Features: ${localFeaturesByPluginId.values.joinToString { "${it.id}/${it.pluginId}: ${it.name}" }}")
        Timber.d("Currently on watch: $pluginsInWatch")
    }

    private suspend fun updateInitialWatchPluginStatuses(
        serial: String,
        capabilities: SuuntoWatchCapabilities
    ) {
        Timber.d("Updating initial plug-in statuses")
        runSuspendCatching {
            val syncFeatureSelection = capabilities.areSuuntoPlusFeaturesSupported
            val syncGuides = capabilities.areSuuntoPlusGuidesSupported

            if (!syncFeatureSelection && !syncGuides) {
                // No plug-in support
                return
            }

            for (plugin in watchDataSource.listPlugins(serial)) {
                if (plugin.isGuide && !plugin.isStatic && syncGuides) {
                    pluginStatusDataSource.updateWatchStatus(
                        watchSerial = serial,
                        pluginId = plugin.id,
                        modifiedMillis = plugin.modificationTimeSeconds * 1000L,
                        capabilities = capabilities,
                        status = SuuntoPlusPluginStatus.IN_WATCH,
                        interestValue = plugin.interestValue,
                        type = SuuntoPlusPluginType.GUIDE,
                    )
                } else if (plugin.isFeatureOrDevice && syncFeatureSelection) {
                    pluginStatusDataSource.updateWatchStatus(
                        watchSerial = serial,
                        pluginId = plugin.id,
                        modifiedMillis = plugin.modificationTimeSeconds * 1000L,
                        capabilities = capabilities,
                        status = SuuntoPlusPluginStatus.IN_WATCH,
                        interestValue = plugin.interestValue,
                        type = SuuntoPlusPluginType.FEATURE,
                    )
                }
            }
        }.onFailure { Timber.w(it, "Failed to query initial watch plug-in status") }
    }

    // Install or upgrade given plug-in. Assumes the the ZAPP file exists in cache. If not, will
    // throw an exception to re-trigger backend sync to download ZAPP files.
    private suspend fun InternalSyncState.installWatchPluginIfNeeded(
        pluginId: String,
        pluginName: String,
        pluginType: SuuntoPlusPluginType,
        modifiedMillis: Long,
        guidesToRemoveIfNeeded: List<String>,
    ) {
        // Watch plug-ins use full seconds for timestamps
        val modifiedSeconds = modifiedMillis / 1000L

        val existingPlugin = pluginsInWatch.current.firstOrNull { it.id == pluginId }
        if (existingPlugin != null && existingPlugin.modificationTimeSeconds >= modifiedSeconds) {
            return // Watch already has the most recent version of this plug-in
        }

        if (existingPlugin == null) {
            Timber.d("Installing new plug-in: $pluginId $pluginName")
        } else {
            Timber.d("Upgrading: $pluginId $pluginName ($modifiedSeconds > ${existingPlugin.modificationTimeSeconds})")
        }

        if (!zappFileStorage.existsInCache(pluginId, modifiedMillis, capabilities)) {
            Timber.w("Missing cached ZAPP file for watch plug-in ID $pluginId")
            triggerBackendSync.set(true)
            throw FileNotFoundException("Missing ZAPP file for watch plug-in ID $pluginId")
        }

        val zappFilePath =
            zappFileStorage.getAbsolutePath(pluginId, modifiedMillis, capabilities, pluginType)
        val zappFileSize = File(zappFilePath).length().toInt()

        // Check for space and delete low priority guides if more space required. Only try to remove
        // plugins already installed in the watch. Features are never removed to make space.
        val removeQueue = guidesToRemoveIfNeeded
            .filter { idToRemove ->
                (currentGuidesInWatch + currentPlansInWatch.values.flatten())
                    .any { watchPlugin -> watchPlugin.id == idToRemove }
            }
            .toMutableList()

        while (!watchDataSource.hasEnoughSpace(serial, zappFileSize) && removeQueue.isNotEmpty()) {
            val idToRemove = removeQueue.removeAt(0)
            runSuspendCatching {
                Timber.d("Removing plugin $idToRemove to make space for $pluginId")
                watchDataSource.uninstallPlugin(serial, idToRemove)
                pluginStatusDataSource.updateWatchStatus(
                    serial,
                    idToRemove,
                    SuuntoPlusPluginStatus.WATCH_FULL
                )
            }.onFailure { e ->
                // Continue sync even if deletion fails
                Timber.w(e, "Failed to delete guide ID $idToRemove")
            }
        }

        runSuspendCatching {
            pluginStatusDataSource.updateWatchStatus(
                serial,
                pluginId,
                SuuntoPlusPluginStatus.INSTALLING
            )
            watchDataSource.installPlugin(serial, pluginId, zappFilePath)
        }.onFailure { e ->
            if (e is WatchPluginStorageFullException) {
                Timber.d("Pushing watch plug-in $pluginId ($pluginName) failed due to running out of space")
                pluginStatusDataSource.updateWatchStatus(
                    serial,
                    pluginId,
                    SuuntoPlusPluginStatus.WATCH_FULL
                )
                return // Consider sync successful if we uploaded all plug-ins that fit
            }

            // Consider other errors as sync failure
            Timber.w(e, "Pushing ZAPP file to watch failed")
            pluginStatusDataSource.updateWatchStatus(
                serial,
                pluginId,
                SuuntoPlusPluginStatus.UNKNOWN
            )
            throw e
        }

        // Watch plug-in pushed successfully
        watchWasUpdated.set(true)
        pluginStatusDataSource.updateWatchStatus(serial, pluginId, SuuntoPlusPluginStatus.IN_WATCH)
        pluginStatusDataSource.updateFileSize(serial, pluginId, zappFileSize)
        Timber.d("Watch plug-in installed successfully: $pluginId $pluginName")
    }

    private suspend fun InternalSyncState.listLocalGuides() =
        localGuidesDataSource.listSuuntoPlusGuidesInGivenStates(
            watchSerial = serial,
            states = setOf(
                SuuntoPlusPluginStatus.IN_WATCH,
                SuuntoPlusPluginStatus.DOWNLOADED,
                SuuntoPlusPluginStatus.WATCH_FULL,
                SuuntoPlusPluginStatus.INSTALLING
            )
        ).associateBy { it.id }

    private suspend fun listLocalPlans(): Map<TrainingPlanId, TrainingPlan> =
        localTrainingPlanDataSource
            .listAllTrainingPlans()
            .first()
            .associateBy { it.id }

    private suspend fun InternalSyncState.listLocalFeatures() =
        localFeaturesDataSource.listSuuntoPlusFeaturesInGivenStates(
            watchSerial = serial,
            states = setOf(
                SuuntoPlusPluginStatus.IN_WATCH,
                SuuntoPlusPluginStatus.DOWNLOADED,
                SuuntoPlusPluginStatus.WATCH_FULL,
                SuuntoPlusPluginStatus.INSTALLING
            )
        )
            .filter {
                // We can't do anything with the default watch face
                it.enabled && it.pluginId != null && it.id != currentWatchfaceId
            }
            .associateBy { it.pluginId!! }

    private suspend fun InternalSyncState.deleteGuidesFromWatch() {
        // Delete removed guides from watch
        val guideIdsToDelete = currentGuidesInWatch.map(WatchPluginInfo::id) -
            localGuidesById.keys.map { it.id }.toSet()
        if (guideIdsToDelete.isNotEmpty()) {
            guideIdsToDelete.forEach { id ->
                Timber.d("Deleting guide $id from watch")
                watchDataSource.uninstallPlugin(serial, id)
                watchWasUpdated.set(true)
            }
        }
    }

    private suspend fun InternalSyncState.deleteTrainingPlanGuidesFromWatch() {
        // Delete removed plan guides from watch
        val planGuideIdsToDelete = currentPlansInWatch.values.flatten().map(WatchPluginInfo::id) -
            localPlansById.allGuideIds().toSet()
        if (planGuideIdsToDelete.isNotEmpty()) {
            planGuideIdsToDelete.forEach { id ->
                Timber.d("Deleting plan guide $id from watch")
                watchDataSource.uninstallPlugin(serial, id)
                watchWasUpdated.set(true)
            }
        }
    }

    private suspend fun InternalSyncState.installFeaturesToWatch(
        watchBusyStateProvider: WatchBusyStateProvider,
    ) {
        // Install new features in alphabetical order just in case the user is following the sync
        // status in the My sports apps list view
        for (feature in localFeaturesByPluginId.values.sortedWith(compareBy(collator) { it.name })) {
            watchBusyStateProvider.throwIfBusy()

            // This is always called for all enabled Features with IN_WATCH or DOWNLOADED device
            // state. installWatchPluginIfNeeded will do nothing for Features that are already
            // installed and don't need upgrading.

            // Consider all SuuntoPlus Guides as expendable when installing Features and running
            // out of space.
            installWatchPluginIfNeeded(
                pluginId = requireNotNull(feature.pluginId),
                pluginName = feature.name,
                pluginType = SuuntoPlusPluginType.FEATURE,
                modifiedMillis = feature.modifiedMillis,
                guidesToRemoveIfNeeded = sortedGuides.map { it.pluginId }
                    .reversed() // Remove lowest priority guides first
            )
        }
    }

    private suspend fun InternalSyncState.installGuidesToWatch(
        watchBusyStateProvider: WatchBusyStateProvider,
    ) {
        for (guide in sortedGuides) {
            watchBusyStateProvider.throwIfBusy()

            // List of guides that may be removed from watch to make space. Sorted by priority
            // (lowest priority first). Does not include the guide being processed currently
            // or anything with a higher priority. May include guide IDs that are not in watch
            // currently.
            val guidesToRemoveIfNeeded = sortedGuides
                .map { it.pluginId }
                .reversed() // Remove lowest priority guides first
                .takeWhile { it != guide.pluginId }

            installWatchPluginIfNeeded(
                pluginId = guide.pluginId,
                pluginName = guide.name,
                pluginType = SuuntoPlusPluginType.GUIDE,
                modifiedMillis = guide.modifiedMillis,
                guidesToRemoveIfNeeded = guidesToRemoveIfNeeded
            )
        }
    }

    private suspend fun InternalSyncState.installPlansToWatch(
        watchBusyStateProvider: WatchBusyStateProvider,
    ) {
        sortedPlans.forEach { plan ->
            watchBusyStateProvider.throwIfBusy()

            val planGuidesToRemoveIfNeeded = sortedPlans
                .reversed()
                .takeWhile { it.pluginId != plan.pluginId }
                .map { it.allGuideIds() }
                .flatten()

            plan.allGuideIds().forEach { guideId ->
                installWatchPluginIfNeeded(
                    pluginId = guideId,
                    pluginName = plan.name,
                    pluginType = SuuntoPlusPluginType.GUIDE,
                    modifiedMillis = plan.modifiedMillis,
                    guidesToRemoveIfNeeded = planGuidesToRemoveIfNeeded
                )
            }
        }
    }

    private suspend fun InternalSyncState.disableExtraFeatures(
        maxFeatureCount: Int,
        maxWatchfaceCount: Int
    ) {
        val localWatchFaces = localFeaturesByPluginId.filter { it.value.isWatchface() }
        val localFeatures = localFeaturesByPluginId - localWatchFaces.keys
        val idsToDisable = mutableListOf<String>()
        // Handle app and watchface number limits separately.
        if (localFeatures.size > maxFeatureCount) {
            val features = localFeatures.values.sortedWith(compareBy(collator) { it.name })
            val featuresToDisable = features.takeLast(features.size - maxFeatureCount)
                .map(SuuntoPlusFeature::id)
            idsToDisable.addAll(featuresToDisable)
        }
        if (localWatchFaces.size > maxWatchfaceCount) {
            val watchFaces = localWatchFaces.values.sortedWith(compareBy(collator) { it.name })
            val watchFacesToDisable = watchFaces.takeLast(watchFaces.size - maxWatchfaceCount)
                .map(SuuntoPlusFeature::id)
            idsToDisable.addAll(watchFacesToDisable)
        }
        if (idsToDisable.isNotEmpty()) {
            localFeaturesDataSource.updateEnabledState(idsToDisable, enabled = false)
        }
    }

    private suspend fun InternalSyncState.deleteFeaturesFromWatch() {
        // Delete removed features from watch
        val featureIdsToDelete =
            currentFeaturesInWatch.map(WatchPluginInfo::id) - localFeaturesByPluginId.keys
        if (featureIdsToDelete.isNotEmpty()) {
            featureIdsToDelete.forEach { id ->
                Timber.d("Deleting feature $id from watch")
                runSuspendCatching {
                    watchDataSource.uninstallPlugin(serial, id)
                    watchWasUpdated.set(true)
                }.onFailure { e ->
                    Timber.w(e, "Failed to uninstall feature plug-in $id")
                }
            }
        }
    }
}

// Abort sync by throwing an exception if the watch is busy. This should only be done in a stage
// where the watch is left in a reasonably consistent state. The next full sync can then continue
// from there.
private suspend fun WatchBusyStateProvider.throwIfBusy() {
    if (isWatchBusy()) {
        Timber.w("Aborting SuuntoPlus plug-in sync. Watch is busy.")
        throw WatchBusyException()
    }
}

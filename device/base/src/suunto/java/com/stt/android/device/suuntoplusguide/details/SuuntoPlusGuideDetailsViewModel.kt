package com.stt.android.device.suuntoplusguide.details

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import com.soy.algorithms.planner.WorkoutPlanner
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.SimGuideMessagesFormatter
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.device.domain.GetWatchCapabilitiesUseCase
import com.stt.android.device.domain.suuntoplusguide.DeleteGuideUseCase
import com.stt.android.device.domain.suuntoplusguide.GetSuuntoPlusGuideUseCase
import com.stt.android.device.domain.suuntoplusguide.IsSuuntoPlusGuideSyncOngoingUseCase
import com.stt.android.device.domain.suuntoplusguide.LimitNumberOfPinnedGuidesUseCase
import com.stt.android.device.domain.suuntoplusguide.SetPinnedStatusForGuideUseCase
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.device.domain.suuntoplusguide.isStravaSegmentsGuide
import com.stt.android.device.domain.suuntoplusguide.isWorkoutPlan
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTrigger
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTriggerImpl
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.suuntoplus.TrainingExtendedData
import com.stt.android.domain.workout.ActivityType
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import javax.inject.Inject

@HiltViewModel
class SuuntoPlusGuideDetailsViewModel @Inject constructor(
    private val getSuuntoPlusGuideUseCase: GetSuuntoPlusGuideUseCase,
    private val setPinnedStatusForGuideUseCase: SetPinnedStatusForGuideUseCase,
    private val limitNumberOfPinnedGuidesUseCase: LimitNumberOfPinnedGuidesUseCase,
    private val deleteGuideUseCase: DeleteGuideUseCase,
    private val isSyncOngoingUseCase: IsSuuntoPlusGuideSyncOngoingUseCase,
    private val currentWatchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
    private val formatter: SimGuideMessagesFormatter,
    private val infoModelFormatter: InfoModelFormatter,
    remoteSyncJobLauncher: SuuntoPlusGuideRemoteSyncJobLauncher,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : LoadingStateViewModel<SuuntoPlusGuideDetailsViewState>(
    ioThread = ioThread,
    mainThread = mainThread
),
    DelayedSuuntoPlusGuideRemoteSyncTrigger by DelayedSuuntoPlusGuideRemoteSyncTriggerImpl(
        remoteSyncJobLauncher
    ) {
    val showMaxPinnedGuidesMessage: State<Boolean>
        get() = _showMaxPinnedGuidesMessage
    private val _showMaxPinnedGuidesMessage = mutableStateOf(false)

    val showGuidePinnedMessage: State<Boolean>
        get() = _showGuidePinnedMessage
    private val _showGuidePinnedMessage = mutableStateOf(false)

    private val modificationTimeFormatter = DateTimeFormatter.ofLocalizedDateTime(
        FormatStyle.SHORT,
        FormatStyle.SHORT
    )

    private fun Long.formatEpochMillisAsDateTime(): String =
        modificationTimeFormatter.format(
            LocalDateTime.ofInstant(
                Instant.ofEpochMilli(this),
                ZoneId.systemDefault()
            )
        )

    private val planner = WorkoutPlanner().apply {
        setFormatter(formatter)
    }

    fun getSimGuideMessagesFormatter(): SimGuideMessagesFormatter {
        return formatter
    }

    fun loadGuideDetails(id: SuuntoPlusGuideId) {
        launch {
            runSuspendCatching {
                combine(
                    getSuuntoPlusGuideUseCase.getGuideAndWatchStatusById(id),
                    isSyncOngoingUseCase.getSyncStateFlow(),
                    ::Pair
                )
                    .catch {
                        if (it !is CancellationException) {
                            Timber.w(it, "Failed to query for guide and watch status")
                            notifyError(it, viewState.value?.data)
                        }
                    }
                    .collect { (guideAndStatus, syncOngoingStatus) ->
                        val guide = guideAndStatus?.guide
                        if (guide == null) {
                            notifyError(NullPointerException(), viewState.value?.data)
                        } else {
                            val workoutItem = guide.catalogueId?.let { getWorkoutItem(it) }
                            notifyDataLoaded(
                                SuuntoPlusGuideDetailsViewState(
                                    title = guide.name,
                                    description = guide.description.takeUnless { it.isBlank() },
                                    richDescription = guide.richDescription?.takeUnless { it.isBlank() },
                                    date = guide.date,
                                    activityTypes = guide.activityIds.orEmpty()
                                        .map { ActivityType.valueOf(it) }
                                        .toImmutableList(),
                                    ownerLogoUrl = guide.iconUrl,
                                    backgroundUrl = guide.backgroundUrl,
                                    url = guide.url,
                                    owner = guide.owner,
                                    pinned = guide.pinned,
                                    isWorkoutPlan = guide.isWorkoutPlan,
                                    isStravaSegmentsGuide = guide.isStravaSegmentsGuide,
                                    watchStatus = guideAndStatus.watchStatus,
                                    syncOngoing = syncOngoingStatus != SuuntoPlusSyncState.IDLE,
                                    modificationTime = guide.modifiedMillis.formatEpochMillisAsDateTime(),
                                    workoutItem = workoutItem
                                )
                            )
                        }
                    }
            }.onFailure { e ->
                Timber.w(e, "Failed to load guide details for id $id")
                notifyError(e, viewState.value?.data)
            }
        }
    }

    fun setPinnedStatus(guideId: SuuntoPlusGuideId, pinned: Boolean) {
        launch {
            runSuspendCatching {
                if (!pinned || limitNumberOfPinnedGuidesUseCase.canOneMoreGuideBePinned()) {
                    if (pinned) {
                        _showGuidePinnedMessage.value = true
                    }
                    setPinnedStatusForGuideUseCase.updatePinnedState(guideId, pinned)

                    triggerSyncWithDelay(coroutineScope = this, alwaysTriggerWatchSync = true)
                } else {
                    _showMaxPinnedGuidesMessage.value = true
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to set pinned flag for guide $guideId")
            }
        }
    }

    suspend fun deleteGuide(guideId: SuuntoPlusGuideId): Boolean =
        runSuspendCatching {
            deleteGuideUseCase.deleteGuide(guideId)
            syncNow(alwaysTriggerWatchSync = false)
            true
        }.getOrElse { e ->
            Timber.w(e, "Failed to delete guide $guideId")
            notifyError(e, viewState.value?.data)
            false
        }

    fun handleMaxPinnedGuidesMessageDismissed() {
        _showMaxPinnedGuidesMessage.value = false
    }

    fun handleGuidePinnedMessageDismissed() {
        _showGuidePinnedMessage.value = false
    }

    override fun retryLoading() = Unit

    private suspend fun getWorkoutItem(suuntoPlusGuideId: String): WorkoutItem? =
        runSuspendCatching {
            val watchCapabilities =
                currentWatchCapabilitiesUseCase.getCurrentCapabilities().capabilities
                    ?: SuuntoWatchCapabilities.EMPTY
            getSuuntoPlusGuideUseCase.fetchSuuntoPlusGuide(suuntoPlusGuideId, watchCapabilities)
        }.getOrNull()?.run {
            this.first?.let { toWorkoutItem(it, this.second) }
        }

    private fun toWorkoutItem(
        json: String,
        extended: TrainingExtendedData?
    ): WorkoutItem {
        planner.startPlanEdit(json)
        val workoutPlan = planner.getWorkoutPlan()

        val phases = workoutPlan.exercises.flatMap { step ->
            when (step) {
                is WorkoutStep.Exercise -> listOf(
                    WorkoutExercise(step.name, step.phase, step.duration, step.target)
                )

                is WorkoutStep.Repeat -> listOf(
                    WorkoutRepetition(
                        step.times,
                        step.exercises.filterIsInstance<WorkoutStep.Exercise>().map {
                            WorkoutExercise(it.name, it.phase, it.duration, it.target)
                        }
                    )
                )

                else -> emptyList()
            }
        }

        val duration = extended?.durationSeconds
            ?.let { infoModelFormatter.formatDuration(it.toLong()) }

        val distance = extended?.distanceMeters
            ?.let { infoModelFormatter.formatDistance(it.toDouble()).getOrNull() }

        return WorkoutItem(
            name = workoutPlan.name,
            iconRes = CoreActivityType.valueOf(workoutPlan.activityType).icon,
            activityColor = CoreActivityType.valueOf(workoutPlan.activityType).color,
            duration = duration,
            distance = distance?.value to distance?.unitResId,
            tss = extended?.tss,
            phases = phases
        )
    }
}

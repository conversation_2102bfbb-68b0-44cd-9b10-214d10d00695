package com.stt.android.device.remote.suuntoplusguide

import android.content.Context
import com.squareup.moshi.Moshi
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.backgroundwork.WorkerKey
import com.stt.android.data.source.local.DaoFactory
import com.stt.android.data.source.local.suuntoplusfeature.SportsAppSettingsStateDao
import com.stt.android.data.source.local.suuntoplusfeature.SuuntoPlusFeatureDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogEventDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusPluginDeviceStatusDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateDao
import com.stt.android.data.source.local.suuntoplusguide.TrainingPlanDao
import com.stt.android.data.source.local.suuntoplusguide.WatchCapabilitiesDao
import com.stt.android.device.CacheDirectory
import com.stt.android.device.datasource.suuntoplusguide.GuideZAPPFileStorage
import com.stt.android.device.datasource.suuntoplusguide.GuideZAPPFileStorageImpl
import com.stt.android.device.datasource.suuntoplusguide.SuuntoWatchCapabilityStore
import com.stt.android.device.datasource.suuntoplusguide.SuuntoWatchCapabilityStoreImpl
import com.stt.android.device.remote.suuntoplusfeature.SuuntoPlusFeaturesRemoteAPI
import com.stt.android.device.remote.suuntoplusfeature.SuuntoPlusFeaturesRemoteAPIImpl
import com.stt.android.device.remote.suuntoplusfeature.SuuntoPlusFeaturesRestAPI
import com.stt.android.device.suuntoplusfeature.AddPrefabricatedWatchfaceToLibraryImpl
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory.getStOkHttpConfig
import com.stt.android.remote.di.RestApiFactory.buildRestApi
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.watch.suuntoplusguide.AddPrefabricatedWatchfaceToLibrary
import com.stt.android.watch.suuntoplusguide.SuuntoPlusGuideWatchSyncProvider
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.multibindings.IntoMap
import okhttp3.OkHttpClient
import java.io.File

@Module
abstract class SuuntoPlusGuideRemoteSyncJobModule {

    @Binds
    @IntoMap
    @WorkerKey(SuuntoPlusGuideRemoteSyncJob::class)
    abstract fun bindFactory(factory: SuuntoPlusGuideRemoteSyncJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    abstract fun bindGuideZAPPFileStorage(storage: GuideZAPPFileStorageImpl): GuideZAPPFileStorage

    @Binds
    abstract fun bindSuuntoPlusGuideRemoteAPI(remoteAPI: SuuntoPlusGuideRemoteAPIImpl): SuuntoPlusGuideRemoteAPI

    @Binds
    abstract fun bindTrainingPlanRemoteAPI(remoteAPI: TrainingPlanRemoteAPIImpl): TrainingPlanRemoteAPI

    @Binds
    abstract fun bindSuuntoPlusFeaturesRemoteAPI(remoteAPI: SuuntoPlusFeaturesRemoteAPIImpl): SuuntoPlusFeaturesRemoteAPI

    @Binds
    abstract fun bindSuuntoPlusGuideCapabilityStore(store: SuuntoWatchCapabilityStoreImpl): SuuntoWatchCapabilityStore

    @Binds
    abstract fun bindSuuntoPlusGuideWatchSyncProvider(suuntoWatchModel: SuuntoWatchModel): SuuntoPlusGuideWatchSyncProvider

    @Binds
    abstract fun bindAddPrefabricatedWatchfaceToLibrary(addPrefabricatedWatchfaceToLibraryImpl: AddPrefabricatedWatchfaceToLibraryImpl): AddPrefabricatedWatchfaceToLibrary

    companion object {

        @Provides
        fun provideTrainingPlanDao(daoFactory: DaoFactory): TrainingPlanDao =
            daoFactory.trainingPlanDao

        @Provides
        fun provideSuuntoPlusGuideDao(daoFactory: DaoFactory): SuuntoPlusGuideDao =
            daoFactory.suuntoPlusGuideDao

        @Provides
        fun provideSuuntoPlusFeatureDao(daoFactory: DaoFactory): SuuntoPlusFeatureDao =
            daoFactory.suuntoPlusFeatureDao

        @Provides
        fun provideSuuntoPlusPluginDeviceStatusDao(daoFactory: DaoFactory): SuuntoPlusPluginDeviceStatusDao =
            daoFactory.suuntoPlusPluginDeviceStatusDao

        @Provides
        fun provideSuuntoPlusGuideSyncLogEventDao(daoFactory: DaoFactory): SuuntoPlusGuideSyncLogEventDao =
            daoFactory.suuntoPlusGuideSyncLogEventDao

        @Provides
        fun provideSuuntoPlusSyncStateDao(daoFactory: DaoFactory): SuuntoPlusSyncStateDao =
            daoFactory.suuntoPlusSyncStateDao

        @Provides
        fun provideWatchCapabilitiesDao(daoFactory: DaoFactory): WatchCapabilitiesDao =
            daoFactory.watchCapabilitiesDao

        @Provides
        fun provideSportsAppSettingsStateDao(daoFactory: DaoFactory): SportsAppSettingsStateDao =
            daoFactory.sportsAppSettingsStateDao

        @Provides
        @CacheDirectory
        fun provideCacheDirectory(appContext: Context): File =
            appContext.cacheDir

        @Provides
        fun provideSuuntoPlusGuideRestAPI(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): SuuntoPlusGuideRestAPI {
            return buildRestApi(
                sharedClient,
                baseUrl,
                SuuntoPlusGuideRestAPI::class.java,
                getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }

        @Provides
        fun provideTrainingPlanRestAPI(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): TrainingPlanRestAPI {
            return buildRestApi(
                sharedClient,
                baseUrl,
                TrainingPlanRestAPI::class.java,
                getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }

        @Provides
        fun provideSuuntoPlusFeaturesRestAPI(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): SuuntoPlusFeaturesRestAPI {
            return buildRestApi(
                sharedClient,
                baseUrl,
                SuuntoPlusFeaturesRestAPI::class.java,
                getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}

package com.stt.android.device.suuntoplusfeature.details

import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.alertColorDarker
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.isKeyboardVisible
import com.stt.android.compose.widgets.ExpandingFloatingActionButton
import com.stt.android.device.R
import com.stt.android.device.domain.ShareSuuntoPlusLinkUseCase
import com.stt.android.device.domain.WatchAndNetworkNotificationStateComposable
import com.stt.android.device.domain.WatchAndNetworkNotificationViewModel
import com.stt.android.device.domain.suuntoplusfeature.isWatchface
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.suuntoplusfeature.SuuntoPlusStoreFeatureRatingDialog
import com.stt.android.device.suuntoplusfeature.settings.SuuntoPlusSettingsViewModel
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
class SuuntoPlusFeatureDetailsFragment : Fragment() {
    private val viewModel: SuuntoPlusFeatureDetailsViewModel by viewModels()
    private val settingsViewModel: SuuntoPlusSettingsViewModel by viewModels()

    private val args: SuuntoPlusFeatureDetailsFragmentArgs by navArgs()

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    @Inject
    lateinit var shareLinkUseCase: dagger.Lazy<ShareSuuntoPlusLinkUseCase>

    private val featureId: String
        get() = args.featureId

    private val navListener = NavController.OnDestinationChangedListener { _, destination, _ ->
        if (destination.id != R.id.featureDetailsFragment) {
            Timber.d("Navigating away from sports app details: triggering settings saving if needed")
            saveIfChanged()
        }
    }

    enum class ConfirmationDialogType {
        NONE,
        CONFIRM_UNINSTALL_FROM_WATCH,
        CONFIRM_REMOVE_FROM_MY_APPS,
        CONFIRM_MANDATORY_SETTINGS_INVALID,
    }

    private val someMandatorySettingMissing: Boolean
        get() {
            val settings = settingsViewModel.viewState.value.data?.settingsItems.orEmpty()
            return settings.any { it.isMandatory && !it.isValid }
        }

    private val confirmationDialogType = mutableStateOf(ConfirmationDialogType.NONE)

    // View model for listening to watch and sync state
    private val watchAndNetworkNotificationViewModel: WatchAndNetworkNotificationViewModel by activityViewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel.loadFeatureDetails()

        savedInstanceState?.getInt(KEY_CONFIRMATION_DIALOG_TEXT, 0)?.let {
            confirmationDialogType.value = ConfirmationDialogType.entries[it]
        }

        setHasOptionsMenu(true)
        findNavController().addOnDestinationChangedListener(navListener)
    }

    override fun onDestroy() {
        super.onDestroy()

        findNavController().removeOnDestinationChangedListener(navListener)
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)

        inflater.inflate(R.menu.item_details_menu, menu)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)

        val viewState = viewModel.viewState.value?.data
        val showShareAndReportAction = viewState != null && !viewState.type.isWatchface()
        menu.findItem(R.id.share_sports_app).isVisible = showShareAndReportAction
        menu.findItem(R.id.report_sports_app).isVisible = showShareAndReportAction
        menu.findItem(R.id.remove_sports_app_from_my_apps).isVisible = viewState != null
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                if (someMandatorySettingMissing) {
                    confirmationDialogType.value = ConfirmationDialogType.CONFIRM_MANDATORY_SETTINGS_INVALID
                    return true
                }
            }

            R.id.share_sports_app -> {
                shareLinkUseCase.get().shareSportsAppLink(requireContext(), featureId)
            }

            R.id.report_sports_app -> {
                val name = viewModel.viewState.value?.data?.title.orEmpty()
                findNavController().navigate(
                    SuuntoPlusFeatureDetailsFragmentDirections.actionFromFeatureDetailsToReport(
                        title = getString(R.string.sports_app_report_screen_title, name),
                        appName = name,
                        appId = featureId
                    )
                )
            }

            R.id.remove_sports_app_from_my_apps -> {
                confirmationDialogType.value = ConfirmationDialogType.CONFIRM_REMOVE_FROM_MY_APPS
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContent {
            if (viewModel.removedFromLibrary) {
                LaunchedEffect(Unit) {
                    if (isAdded && findNavController().currentDestination?.id == R.id.featureDetailsFragment) {
                        findNavController().popBackStack()
                    }
                }
            }

            AppTheme {
                val scaffoldState = rememberScaffoldState()
                Scaffold(
                    scaffoldState = scaffoldState
                ) { internalPadding ->
                    ContentCenteringColumn(Modifier.padding(internalPadding)) {
                        Surface {
                            val coroutineScope = rememberCoroutineScope()
                            val state by viewModel.viewState.observeAsState()
                            val userRating by viewModel.userRating.collectAsState()

                            LaunchedEffect(key1 = state) {
                                requireActivity().invalidateOptionsMenu()
                            }

                            // Load settings every time pluginId changes or watch connects
                            val notConnected = state?.data?.showWatchNotConnectedMessage
                            val watchBusy = state?.data?.showWatchBusyMessage
                            val installed = state?.data?.watchStatus == SuuntoPlusPluginStatus.IN_WATCH
                            val isWatchface = state?.data?.type?.isWatchface() ?: false
                            val isCurrentWatchface = state?.data?.isCurrentWatchface ?: false
                            LaunchedEffect(viewModel.pluginId, notConnected, watchBusy, installed) {
                                if (state?.data?.supportsSettings == true) {
                                    viewModel.pluginId?.let { pluginId ->
                                        settingsViewModel.lockSportsAndLoadCurrentValuesIfNeeded(
                                            featureId = featureId,
                                            pluginId = pluginId
                                        )
                                    }
                                }
                            }

                            var showRatingDialog by rememberSaveable { mutableStateOf(false) }

                            // Unlock settings if watch becomes busy
                            if (watchBusy == true && state?.isLoaded() == true && settingsViewModel.viewState.value.isLoaded()) {
                                LaunchedEffect(watchBusy, state?.isLoaded(), settingsViewModel.viewState.value.isLoaded()) {
                                    settingsViewModel.unlockSportsApp()
                                }
                            }

                            // Show error messages
                            val error = (state as? ViewState.Error)?.errorEvent
                            if (error != null && error.shouldHandle) {
                                LaunchedEffect(error) {
                                    scaffoldState.snackbarHostState.showSnackbar(
                                        getString(error.errorStringRes)
                                    )
                                }
                            }
                            when {
                                state?.isLoading() == true -> {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .wrapContentSize(Alignment.Center)
                                    ) {
                                        if (LocalInspectionMode.current) {
                                            // Make the indicator more visible on preview
                                            CircularProgressIndicator(progress = 1f)
                                        } else {
                                            CircularProgressIndicator()
                                        }
                                    }
                                }

                                state is ViewState.Loaded -> {
                                    Box {
                                        state?.data?.let { feature ->
                                            val lazyListState = rememberLazyListState()
                                            val expandButton = remember {
                                                derivedStateOf {
                                                    lazyListState.firstVisibleItemIndex == 0 && lazyListState.firstVisibleItemScrollOffset == 0
                                                }
                                            }

                                            val settingsViewState = settingsViewModel.viewState.value

                                            val enableSettings = !feature.syncOngoing &&
                                                settingsViewState.data?.enabled == true &&
                                                !feature.showWatchNotConnectedMessage &&
                                                watchBusy != true &&
                                                feature.enabled &&
                                                feature.watchStatus == SuuntoPlusPluginStatus.IN_WATCH

                                            if (!isWatchface) {
                                                SuuntoPlusLocalFeatureDetails(
                                                    title = feature.title,
                                                    description = feature.description,
                                                    richDescription = viewModel.currentDescriptionText,
                                                    bannerImageUrl = feature.bannerImageUrl,
                                                    watchPreviewImageUrl = feature.detailScreenImageUrl,
                                                    learnMoreUrl = feature.url,
                                                    watchStatus = feature.watchStatus,
                                                    formattedModificationTime = feature.modificationTime,
                                                    onLinkClick = {
                                                        CustomTabsUtils.launchCustomTab(
                                                            requireContext(),
                                                            it
                                                        )
                                                    },
                                                    supportsSettings = feature.supportsSettings,
                                                    enableSettings = enableSettings,
                                                    variables = settingsViewState.data?.variableItems ?: persistentListOf(),
                                                    settings = settingsViewState.data?.settingsItems ?: persistentListOf(),
                                                    settingsNonce = settingsViewState.data?.settingValueNonce ?: 0,
                                                    onSettingChange = { path, value ->
                                                        settingsViewModel.modifySetting(
                                                            path = path,
                                                            newValue = value
                                                        )
                                                    },
                                                    showWatchNotConnectedMessage = feature.showWatchNotConnectedMessage,
                                                    showWatchBusyMessage = feature.showWatchBusyMessage,
                                                    lazyListState = lazyListState,
                                                    modifier = Modifier.fillMaxSize(),
                                                    autoTranslate = feature.localizedRichTextAutomatically ?: false,
                                                    showTranslatedText = viewModel.isTranslated,
                                                    onClickCallback = {
                                                        viewModel.changeLanguage(it)
                                                    },
                                                    showRating = viewModel.suuntoPlusFeedbackEnabled,
                                                    avgRating = feature.rating?.avgRating,
                                                    ratingReviews = feature.rating?.reviews,
                                                    onRatingClick = {
                                                        showRatingDialog = true
                                                    },
                                                )

                                                if (!isKeyboardVisible().value) {
                                                    if (feature.installOnWatch) {
                                                        ExpandingFloatingActionButton(
                                                            drawableResource = R.drawable.remove_from_watch_outline,
                                                            text = stringResource(id = R.string.suunto_plus_floating_action_button_uninstall_from_watch),
                                                            expanded = expandButton.value,
                                                            enabled = feature.enableUninstallButton,
                                                            contentColor = MaterialTheme.colors.alertColorDarker,
                                                            onClick = {
                                                                confirmationDialogType.value =
                                                                    ConfirmationDialogType.CONFIRM_UNINSTALL_FROM_WATCH
                                                            },
                                                            modifier = Modifier
                                                                .align(Alignment.BottomEnd)
                                                                .padding(
                                                                    bottom = MaterialTheme.spacing.large,
                                                                    end = MaterialTheme.spacing.medium
                                                                )
                                                        )
                                                    } else {
                                                        ExpandingFloatingActionButton(
                                                            drawableResource = R.drawable.transfer_to_watch_outline,
                                                            text = stringResource(id = R.string.suunto_plus_floating_action_button_install_on_watch),
                                                            expanded = expandButton.value,
                                                            enabled = feature.enableInstallButton,
                                                            contentColor = MaterialTheme.colors.onSurface,
                                                            onClick = {
                                                                viewModel.updateEnabledState(featureId, true)
                                                                coroutineScope.launch {
                                                                    watchAndNetworkNotificationViewModel.showToastMessage(
                                                                        message = getString(
                                                                            R.string.suunto_plus_guide_or_sports_app_added_to_watch,
                                                                            feature.title
                                                                        )
                                                                    )
                                                                }
                                                            },
                                                            modifier = Modifier
                                                                .align(Alignment.BottomEnd)
                                                                .padding(
                                                                    bottom = MaterialTheme.spacing.large,
                                                                    end = MaterialTheme.spacing.medium
                                                                )
                                                        )
                                                    }
                                                }
                                            } else {
                                                SuuntoPlusLocalWatchfaceDetails(
                                                    watchStatus = feature.watchStatus,
                                                    isCurrentWatchface = isCurrentWatchface,
                                                    watchPreviewImageUrl = feature.detailScreenImageUrl,
                                                    title = feature.title,
                                                    onSetAsCurrentWatchfaceClick = { viewModel.setAsCurrentWatchface() },
                                                    showSetAsCurrentWatchfaceButton = feature.showSetAsCurrentWatchfaceButton,
                                                    installOnWatch = feature.installOnWatch,
                                                    enableInstallButton = feature.enableInstallButton,
                                                    onInstallClick = {
                                                        viewModel.updateEnabledState(featureId, true)
                                                        coroutineScope.launch {
                                                            watchAndNetworkNotificationViewModel.showToastMessage(
                                                                message = getString(
                                                                    R.string.suunto_plus_guide_or_sports_app_added_to_watch,
                                                                    feature.title
                                                                )
                                                            )
                                                        }
                                                    },
                                                    enableUninstallButton = feature.enableUninstallButton,
                                                    onUninstallClick = {
                                                        confirmationDialogType.value =
                                                            ConfirmationDialogType.CONFIRM_UNINSTALL_FROM_WATCH
                                                    },
                                                    haveSpaceForInstalling = feature.haveSpaceForInstalling,
                                                )
                                            }

                                            BackHandler(enabled = someMandatorySettingMissing) {
                                                confirmationDialogType.value = ConfirmationDialogType.CONFIRM_MANDATORY_SETTINGS_INVALID
                                            }
                                        }

                                        val watchAndNetworkState =
                                            watchAndNetworkNotificationViewModel.watchAndNetworkNotificationState
                                                .collectAsState()
                                        WatchAndNetworkNotificationStateComposable(
                                            state = watchAndNetworkState.value,
                                            modifier = Modifier
                                                .align(Alignment.TopCenter)
                                                .fillMaxWidth()
                                                .padding(
                                                    top = MaterialTheme.spacing.medium,
                                                    start = MaterialTheme.spacing.large,
                                                    end = MaterialTheme.spacing.large,
                                                ),
                                            watchBusyMessage = stringResource(BaseR.string.suunto_plus_sync_watch_busy),
                                            watchDisconnectedMessage = stringResource(BaseR.string.sport_mode_watch_disconnected),
                                            watchSyncingMessage = stringResource(BaseR.string.sport_mode_watch_syncing)
                                        )
                                    }
                                }
                            }

                            if (showRatingDialog) {
                                SuuntoPlusStoreFeatureRatingDialog(
                                    initialRating = userRating?.rating,
                                    initialComment = userRating?.comment,
                                    onDismiss = { showRatingDialog = false },
                                    onSubmit = { rating, comment ->
                                        showRatingDialog = false
                                        viewModel.rateFeature(rating, comment)
                                    },
                                )
                            }

                            when (confirmationDialogType.value) {
                                ConfirmationDialogType.CONFIRM_UNINSTALL_FROM_WATCH -> ConfirmUninstallDialog(
                                    onUninstallClick = {
                                        confirmationDialogType.value = ConfirmationDialogType.NONE
                                        viewModel.updateEnabledState(featureId, false)
                                        coroutineScope.launch {
                                            watchAndNetworkNotificationViewModel.showToastMessage(
                                                message = getString(
                                                    R.string.suunto_plus_guide_or_sports_app_removed_from_watch,
                                                    state?.data?.title.orEmpty()
                                                )
                                            )
                                        }
                                    },
                                    onCancelClick = {
                                        confirmationDialogType.value = ConfirmationDialogType.NONE
                                    },
                                    isWatchface = isWatchface,
                                )

                                ConfirmationDialogType.CONFIRM_REMOVE_FROM_MY_APPS -> ConfirmRemoveDialog(
                                    onRemoveClick = {
                                        viewModel.removeFeatureFromLibrary(featureId)
                                        // viewModel.removedFromLibrary will trigger back navigation
                                    },
                                    onCancelClick = {
                                        confirmationDialogType.value = ConfirmationDialogType.NONE
                                    },
                                    isWatchface = isWatchface,
                                )

                                // Watch face won't get here?
                                ConfirmationDialogType.CONFIRM_MANDATORY_SETTINGS_INVALID -> ConfirmMandatorySettingsInvalidDialog(
                                    onSetUpClick = {
                                        confirmationDialogType.value = ConfirmationDialogType.NONE
                                        // TODO: when using LazyColumn, scroll down the first mandatory setting
                                    },
                                    onDismissClick = {
                                        findNavController().popBackStack()
                                    },
                                )

                                ConfirmationDialogType.NONE -> {}
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onStop() {
        super.onStop()

        viewModel.syncNowIfDelayedSyncRequested()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt(KEY_CONFIRMATION_DIALOG_TEXT, confirmationDialogType.value.ordinal)
    }

    private fun saveIfChanged() {
        val appContext = requireContext().applicationContext
        settingsViewModel.saveCompleteListener = { result, name ->
            result.onSuccess {
                val text = getString(R.string.suunto_plus_sports_app_settings_saved_to_watch, name)
                Toast.makeText(appContext, text, Toast.LENGTH_SHORT).show()
            }.onFailure {
                val text = getString(R.string.suunto_plus_sports_app_failed_to_save_settings_to_watch, name)
                Toast.makeText(appContext, text, Toast.LENGTH_LONG).show()
            }
        }
        settingsViewModel.saveInBackgroundIfChanged()
    }

    companion object {
        private const val KEY_CONFIRMATION_DIALOG_TEXT = "com.stt.android.device.KEY_CONFIRMATION_DIALOG_TEXT"
    }
}

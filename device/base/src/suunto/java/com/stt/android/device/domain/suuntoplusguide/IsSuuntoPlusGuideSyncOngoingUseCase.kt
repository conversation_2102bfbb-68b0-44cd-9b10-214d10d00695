package com.stt.android.device.domain.suuntoplusguide

import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateRepository
import com.stt.android.device.datasource.WatchSerialDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import timber.log.Timber
import javax.inject.Inject

class IsSuuntoPlusGuideSyncOngoingUseCase @Inject constructor(
    private val syncStateRepository: SuuntoPlusSyncStateRepository,
    private val watchSerialDataSource: WatchSerialDataSource,
) {
    fun getSyncStateFlow(): Flow<SuuntoPlusSyncState> =
        watchSerialDataSource.getCurrentWatchSerialAsFlow()
            .flatMapLatest { serial ->
                if (serial != null) {
                    syncStateRepository.getSyncStateAsFlow(serial)
                } else {
                    flowOf(SuuntoPlusSyncState.IDLE)
                }
            }
            .catch {
                Timber.w(it, "Failed to listen to sync state flow")
                emit(SuuntoPlusSyncState.IDLE)
            }
            .distinctUntilChanged()
}

enum class SuuntoPlusGuideSyncState {
    IDLE,
    WATCH_SYNC_ONGOING,
    REMOTE_SYNC_ONGOING;

    val isSyncOngoing: Boolean
        get() = this != IDLE
}

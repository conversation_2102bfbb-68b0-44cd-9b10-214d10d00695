package com.stt.android.device.watchface

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.combine
import com.stt.android.data.source.local.watchface.WatchFaceSyncState
import com.stt.android.device.domain.watchface.GetCurrentWatchFaceIdUseCase
import com.stt.android.device.domain.watchface.GetMyWatchFacesUseCase
import com.stt.android.device.domain.watchface.IsWatchFaceSyncOngoingUseCase
import com.stt.android.device.domain.watchface.MyWatchFacesListContainer
import com.stt.android.device.domain.watchface.NumberOfEnabledWatchFacesUseCase
import com.stt.android.device.domain.watchface.SetEnabledStateForWatchFaceUseCase
import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.remote.watchface.DelayWatchFaceRemoteSyncTrigger
import com.stt.android.device.remote.watchface.DelayWatchFaceRemoteSyncTriggerImpl
import com.stt.android.domain.watch.IsWatchBusyUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.watch.background.WatchFaceRemoteSyncJobLauncher
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class MyWatchFaceViewModel @Inject constructor(
    private val getMyWatchFacesUseCase: GetMyWatchFacesUseCase,
    remoteSyncJobLauncher: WatchFaceRemoteSyncJobLauncher,
    private val isWatchBusyUseCase: IsWatchBusyUseCase,
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val getCurrentWatchFaceIdUseCase: GetCurrentWatchFaceIdUseCase,
    private val setEnabledStateForWatchFaceUseCase: SetEnabledStateForWatchFaceUseCase,
    private val isSyncOngoingUseCase: IsWatchFaceSyncOngoingUseCase,
    private val numberOfEnabledWatchFacesUseCase: NumberOfEnabledWatchFacesUseCase,
) : ViewModel(),
    DelayWatchFaceRemoteSyncTrigger by DelayWatchFaceRemoteSyncTriggerImpl(
        remoteSyncJobLauncher
    ) {

    private val syncRequired = MutableStateFlow(false)
    private var loadDataJob: Job? = null

    data class CombinedWatchFaceContainer(
        val watchFaceContainer: GetMyWatchFacesUseCase.WatchFacesContainer,
        val syncState: WatchFaceSyncState,
        val watchBusy: Boolean,
        val watchDisconnected: Boolean,
        val syncRequired: Boolean,
        val currentWatchfaceId: String?,
    ) {
        companion object {
            val EMPTY = CombinedWatchFaceContainer(
                watchFaceContainer = GetMyWatchFacesUseCase.WatchFacesContainer.EMPTY,
                syncState = WatchFaceSyncState.IDLE,
                watchBusy = false,
                watchDisconnected = false,
                syncRequired = false,
                currentWatchfaceId = null,
            )
        }
    }

    sealed class ViewState {
        object Loading : ViewState()
        data class Loaded(val data: MyWatchFacesListContainer) : ViewState()
        data class Error(val error: Throwable) : ViewState()
    }

    enum class Notification {
        NONE,
        SYNCING,
        BUSY,
        DISCONNECTED,
        SYNC_NOW_BUTTON,
        WATCH_FULL,
    }

    private val _viewState = MutableStateFlow<ViewState>(ViewState.Loading)
    val viewState: StateFlow<ViewState> = _viewState.asStateFlow()

    init {
        loadData()
    }

    private fun loadData() {
        syncWithRemote()
        loadDataJob?.cancel()
        loadDataJob = viewModelScope.launch {
            combine(
                getMyWatchFacesUseCase.listMyWatchFacesAsFlow(),
                isSyncOngoingUseCase.getSyncStateFlow(),
                isWatchBusyUseCase(),
                isWatchConnectedUseCase.invoke(),
                syncRequired,
                getCurrentWatchFaceIdUseCase(),
                ::CombinedWatchFaceContainer
            )
                .catch {
                    Timber.w(it, "Failed to load Features list")
                    emit(CombinedWatchFaceContainer.EMPTY)
                }
                .onEach { (_, syncState, _, _) ->
                    if (syncState.isSyncOngoing) {
                        syncRequired.value = false
                    }
                }
                .collect { (watchFacesContainer, syncState, watchBusy, watchConnected, syncRequired, currentWatchFaceId) ->
                    val (watchFacesOnWatchCount, watchFacesOnWatchCountMax) =
                        numberOfEnabledWatchFacesUseCase.numberOfEnabledAndMaxWatchFaces()

                    val notificationState = when {
                        syncState.isSyncOngoing -> Notification.SYNCING
                        watchBusy -> Notification.BUSY
                        !watchConnected -> Notification.DISCONNECTED
                        syncRequired -> Notification.SYNC_NOW_BUTTON
                        else -> Notification.NONE
                    }

                    _viewState.value = ViewState.Loaded(
                        MyWatchFacesListContainer(
                            watchFaces = watchFacesContainer.watchFaces.toImmutableList(),
                            syncState = syncState,
                            watchBusy = watchBusy,
                            watchDisconnected = !watchConnected,
                            showSyncNowButton = syncRequired,
                            refreshing = syncState == WatchFaceSyncState.REMOTE_SYNC_ONGOING,
                            showWatchFullNotification = watchFacesContainer.watchSupportsFeatures && !numberOfEnabledWatchFacesUseCase.canOneMoreWatchFaceBeEnabled(),
                            canEnableMoreFeatures = numberOfEnabledWatchFacesUseCase.canOneMoreWatchFaceBeEnabled(),
                            watchFacesOnWatchCount = watchFacesOnWatchCount,
                            watchFacesOnWatchCountMax = watchFacesOnWatchCountMax,
                            currentWatchFaceId = if (watchConnected) currentWatchFaceId else null,
                            notificationState = notificationState,
                        )
                    )
                }
        }
    }

    fun addToWatch(watchFace: WatchFace) = updateAddToWatchState(watchFace, true)

    fun removeFromWatch(watchFace: WatchFace) = updateAddToWatchState(watchFace, false)

    private fun updateAddToWatchState(watchFace: WatchFace, enabled: Boolean) =
        viewModelScope.launch {
            try {
                setEnabledStateForWatchFaceUseCase.updateAddToWatchState(watchFace.id, enabled)
                triggerSyncWithDelay(coroutineScope = this, alwaysTriggerWatchSync = true)
                syncRequired.value = true
            } catch (e: Exception) {
                Timber.w(e, "Failed to update addToWatch flag for watchFace: ${watchFace.id}, ${watchFace.name}")
            }
        }

    fun syncWithRemote(alwaysTriggerWatchSync: Boolean = isDelayedSyncRequested()) {
        // Enable alwaysTriggerWatchSync by default in case user has toggled any of the enabled switches
        syncRequired.value = false
        syncNow(alwaysTriggerWatchSync = alwaysTriggerWatchSync)
    }

    fun resetSyncRequiredFlag() {
        syncRequired.value = false
    }

    override fun onCleared() {
        super.onCleared()
        loadDataJob?.cancel()
        clearDelayedSync()
    }
}

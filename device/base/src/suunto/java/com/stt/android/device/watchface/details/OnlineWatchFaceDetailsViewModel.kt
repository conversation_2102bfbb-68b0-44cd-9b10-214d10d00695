package com.stt.android.device.watchface.details

import androidx.annotation.StringRes
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.domain.GetWatchCapabilitiesUseCase
import com.stt.android.device.domain.watchface.AddWatchFaceToLibraryUseCase
import com.stt.android.device.domain.watchface.GetCurrentWatchFaceIdUseCase
import com.stt.android.device.domain.watchface.GetWatchFaceUseCase
import com.stt.android.device.domain.watchface.NumberOfEnabledWatchFacesUseCase
import com.stt.android.device.domain.watchface.OnlineWatchFaceUseCase
import com.stt.android.device.domain.watchface.RemoveWatchFaceFromLibraryUseCase
import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.domain.watchface.WatchFaceAndStatus
import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.stt.android.device.domain.watchface.WatchFaceStatus
import com.stt.android.device.remote.watchface.DelayWatchFaceRemoteSyncTrigger
import com.stt.android.device.remote.watchface.DelayWatchFaceRemoteSyncTriggerImpl
import com.stt.android.device.watchface.LibraryOperationViewState
import com.stt.android.device.watchface.note.WatchFaceNote
import com.stt.android.domain.watch.IsWatchBusyUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.watch.background.WatchFaceRemoteSyncJobLauncher
import com.stt.android.device.R
import com.stt.android.device.domain.suuntoplusguide.IsSuuntoPlusGuideSyncOngoingUseCase
import com.stt.android.device.domain.watchface.IsWatchFaceSyncOngoingUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class OnlineWatchFaceDetailsViewModel @Inject constructor(
    private val onlineWatchFaceUseCase: OnlineWatchFaceUseCase,
    private val getWatchFaceUseCase: GetWatchFaceUseCase,
    private val getCurrentWatchFaceIdUseCase: GetCurrentWatchFaceIdUseCase,
    private val currentWatchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
    private val addWatchFaceToLibraryUseCase: AddWatchFaceToLibraryUseCase,
    private val removeWatchFaceFromLibraryUseCase: RemoveWatchFaceFromLibraryUseCase,
    private val watchFaceRemoteSyncJobLauncher: WatchFaceRemoteSyncJobLauncher,
    private val numberOfWatchFacesInWatchUseCase: NumberOfEnabledWatchFacesUseCase,
    private val watchConnectedUseCase: IsWatchConnectedUseCase,
    private val isWatchBusyUseCase: IsWatchBusyUseCase,
    private val isSyncOngoingUseCase: IsWatchFaceSyncOngoingUseCase,
) : ViewModel(), DelayWatchFaceRemoteSyncTrigger by DelayWatchFaceRemoteSyncTriggerImpl(
    watchFaceRemoteSyncJobLauncher
) {
    sealed class ViewData {
        data object Loading : ViewData()

        data class Loaded(
            val watchFaceEntity: WatchFaceEntity,
            val watchFaceStatus: WatchFaceStatus,
            val isCurrentWatchFace: Boolean,
            val showAddToLibraryAction: Boolean,
            val showRemoveFromLibraryAction: Boolean,
            val showInstallButton: Boolean,
            val showSetAsCurrentWatchfaceButton: Boolean,
            val enableInstallButton: Boolean,
            val enableUninstallButton: Boolean,
            val watchFaceNotes: ImmutableList<WatchFaceNote> = persistentListOf(),
            val onInstallClick: () -> Unit,
            val onSetAsCurrentWatchfaceClick: () -> Unit,
            val onUninstallClick: () -> Unit,
            val confirmData: ConfirmData? = null,
        ) : ViewData()

        data class Failed(val errorMsg: String) : ViewData()
    }

    sealed class ConfirmData(
        @param:StringRes open val confirmationTextResId: Int,
        open val onConfirm: () -> Unit,
        open val onCancel: () -> Unit,
    ) {
        data class ConfirmRemoveFromLibrary(
            override val onConfirm: () -> Unit,
            override val onCancel: () -> Unit,
        ) : ConfirmData(
            R.string.watch_face_remove_from_library_confirm_content,
            onConfirm,
            onCancel
        )

        data class ConfirmRemoveFromWatch(
            override val onConfirm: () -> Unit,
            override val onCancel: () -> Unit,
        ) : ConfirmData(
            R.string.watch_face_remove_from_watch_confirm_content,
            onConfirm,
            onCancel
        )
    }

    private val _viewState: MutableStateFlow<ViewData> = MutableStateFlow(ViewData.Loading)
    val viewState: StateFlow<ViewData> = _viewState.asStateFlow()

    private val _libraryOperationViewState: MutableStateFlow<LibraryOperationViewState?> =
        MutableStateFlow(null)
    val libraryOperationViewState: StateFlow<LibraryOperationViewState?> =
        _libraryOperationViewState.asStateFlow()

    private val watchFaceAndStatusFlow = MutableStateFlow<WatchFaceAndStatus?>(null)
    private var loadWatchFaceAndStatusJob: Job? = null

    private var watchFace: WatchFace? = null
    private var libraryOperationJob: Job? = null

    private fun loadWatchFaceAndStatus(id: String) {
        loadWatchFaceAndStatusJob?.cancel()
        loadWatchFaceAndStatusJob = viewModelScope.launch {
            runSuspendCatching {
                getWatchFaceUseCase.getWatchFaceAndStatusById(id).collect {
                    watchFaceAndStatusFlow.value = it
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to load watch face for id $id")
            }
        }
    }

    fun loadWatchFaceDetails(watchFace: WatchFace) = viewModelScope.launch {
        <EMAIL> = watchFace

        runSuspendCatching {
            val remoteId = watchFace.id
            loadWatchFaceAndStatus(remoteId)

            combine(
                watchFaceAndStatusFlow.drop(1),
                watchConnectedUseCase.invoke(),
                isWatchBusyUseCase(),
                isSyncOngoingUseCase.getSyncStateFlow(),
                getCurrentWatchFaceIdUseCase(),
            ) { watchFaceAndStatus, watchConnected, watchBusy, syncOngoingStatus, currentWatchFaceId ->
                val remoteWatchFace =
                    watchFaceAndStatus?.watchFace ?: throw NullPointerException("")
                val (_, watchCapabilities) = currentWatchCapabilitiesUseCase.getCurrentCapabilities()

                val isRunWatchFaceSupported = watchCapabilities?.isRunWatchFaceSupported == true
                val isSyncOngoing = syncOngoingStatus.isSyncOngoing
                val watchFaceStatus = watchFaceAndStatus.watchStatus
                val watchFaceSupported = remoteWatchFace.supported
                val addToWatch = remoteWatchFace.addToWatch
                val isWatchFaceInWatch = watchFaceStatus == WatchFaceStatus.IN_WATCH
                val isCurrentWatchFace = currentWatchFaceId == remoteWatchFace.watchfaceId
                val showSetAsCurrentWatchfaceButton =
                    watchFaceSupported && !isCurrentWatchFace && addToWatch && isWatchFaceInWatch
                val isReachMaxLimit =
                    !numberOfWatchFacesInWatchUseCase.canOneMoreWatchFaceBeEnabled()
                val showInstallButton = watchFaceSupported && !addToWatch
                val watchFaceNotes = when {
                    !isRunWatchFaceSupported || !watchFaceSupported ->
                        persistentListOf(WatchFaceNote.InCompatible)

                    !addToWatch && isReachMaxLimit ->
                        persistentListOf(WatchFaceNote.MaxLimitReachedNote)

                    else -> persistentListOf()
                }

                ViewData.Loaded(
                    watchFaceEntity = remoteWatchFace,
                    watchFaceStatus = watchFaceStatus,
                    isCurrentWatchFace = isCurrentWatchFace,
                    showAddToLibraryAction = remoteWatchFace.addToFavorite,
                    showRemoveFromLibraryAction = !remoteWatchFace.addToFavorite,
                    showInstallButton = showInstallButton,
                    showSetAsCurrentWatchfaceButton = showSetAsCurrentWatchfaceButton,
                    enableInstallButton = watchFaceSupported && !isReachMaxLimit && !isSyncOngoing,
                    enableUninstallButton = watchFaceSupported && !isSyncOngoing,
                    watchFaceNotes = watchFaceNotes,
                    onInstallClick = ::handleInstallClick,
                    onSetAsCurrentWatchfaceClick = ::handleSetAsCurrentClick,
                    onUninstallClick = ::handleUninstallClick
                )
            }.catch {
                Timber.w(it, "Failed to load watch face details")
            }.collect {
                _viewState.value = it
            }
        }.onFailure { error ->
            Timber.w(error, "Failed to load watch face details")
            _viewState.value = ViewData.Failed(error.message ?: "Unknown error")
        }
    }

    private fun handleInstallClick() {
        (_viewState.value as? ViewData.Loaded)?.let {
            addToWatch(it.watchFaceEntity)
        }
    }

    private fun handleSetAsCurrentClick() {
        (_viewState.value as? ViewData.Loaded)?.let {
            setAsCurrentWatchFace(it.watchFaceEntity)
        }
    }

    private fun handleUninstallClick() {
        (_viewState.value as? ViewData.Loaded)?.let {
            val confirmData = ConfirmData.ConfirmRemoveFromWatch(
                onConfirm = {
                    removeFromWatchInternal(it.watchFaceEntity)
                },
                onCancel = {
                    cancelConfirm()
                }
            )
            _viewState.value = it.copy(confirmData = confirmData)
        }
    }

    private fun setAsCurrentWatchFace(watchFace: WatchFaceEntity) = viewModelScope.launch {
        runSuspendCatching {
            onlineWatchFaceUseCase.setWatchFaceAsCurrent(watchFace.watchfaceId)
        }.onSuccess {
            Timber.d("setAsCurrentWatchFace successful.")
            // Refresh the view state to update button states
            val viewData = (_viewState.value as? ViewData.Loaded)?.copy(
                showSetAsCurrentWatchfaceButton = false,
            )
            viewData?.let { _viewState.value = it }
        }.onFailure { error ->
            Timber.w(error, "setAsCurrentWatchFace failed.")
        }
    }

    private fun addToWatch(watchFace: WatchFaceEntity) {
        launchLibraryOperation(
            watchFace = watchFace,
            operation = LibraryOperationViewState.Operation.ADD_TO_WATCH,
            onComplete = { refresh(alwaysTriggerWatchSync = true) }
        ) {
            addWatchFaceToLibraryUseCase.addWatchFaceToWatch(
                id = watchFace.runFeatureCatalogueId,
                watchCapabilities = watchFace.targetInstallCapability
            )
        }
    }

    fun addToLibrary() {
        val watchFace = (_viewState.value as? ViewData.Loaded)?.watchFaceEntity ?: return
        launchLibraryOperation(
            watchFace = watchFace,
            operation = LibraryOperationViewState.Operation.ADD_TO_LIBRARY,
            onComplete = { refresh(alwaysTriggerWatchSync = true) }
        ) {
            addWatchFaceToLibraryUseCase.addWatchFaceToLibrary(
                id = watchFace.runFeatureCatalogueId,
                watchCapabilities = watchFace.targetInstallCapability,
                addToWatch = watchFace.addToWatch
            )

        }
    }

    fun removeFromLibrary() {
        (_viewState.value as? ViewData.Loaded)?.let {
            val confirmData = if (it.watchFaceEntity.addToWatch) {
                ConfirmData.ConfirmRemoveFromLibrary(
                    onConfirm = {
                        removeFromWatchInternal(it.watchFaceEntity)
                    },
                    onCancel = {
                        cancelConfirm()
                    }
                )
            } else {
                ConfirmData.ConfirmRemoveFromLibrary(
                    onConfirm = {
                        removeFromLibraryInternal(it.watchFaceEntity)
                    },
                    onCancel = {
                        cancelConfirm()
                    }
                )
            }
            _viewState.value = it.copy(confirmData = confirmData)
        }
    }

    private fun removeFromLibraryInternal(watchFace: WatchFaceEntity) {
        launchLibraryOperation(
            watchFace = watchFace,
            operation = LibraryOperationViewState.Operation.REMOVE_FROM_LIBRARY,
            onComplete = { refresh(alwaysTriggerWatchSync = false) }
        ) {
            removeWatchFaceFromLibraryUseCase.removeWatchFaceFromLibrary(
                id = watchFace.runFeatureCatalogueId,
                watchFaceCapabilities = watchFace.targetInstallCapability,
                addToWatch = false
            )
        }
    }

    private fun removeFromWatchInternal(watchFace: WatchFaceEntity) {
        launchLibraryOperation(
            watchFace = watchFace,
            operation = LibraryOperationViewState.Operation.REMOVE_FROM_WATCH,
            onComplete = { refresh(alwaysTriggerWatchSync = true) }
        ) {
            removeWatchFaceFromLibraryUseCase.removeWatchFaceFromWatch(
                id = watchFace.runFeatureCatalogueId,
                watchFaceCapabilities = watchFace.targetInstallCapability,
                addToFavorite = watchFace.addToFavorite
            )
        }
    }

    private fun cancelConfirm() {
        (_viewState.value as? ViewData.Loaded)?.let {
            _viewState.value = it.copy(confirmData = null)
        }
    }

    private fun launchLibraryOperation(
        watchFace: WatchFaceEntity,
        operation: LibraryOperationViewState.Operation,
        onComplete: () -> Unit,
        block: suspend () -> Unit
    ) {
        _libraryOperationViewState.value = LibraryOperationViewState(
            state = LibraryOperationViewState.State.IN_PROGRESS,
            operation = operation,
            watchFaceName = watchFace.name,
        )

        val oldJob = libraryOperationJob
        libraryOperationJob?.cancel()
        libraryOperationJob = viewModelScope.launch {
            oldJob?.join()
            _libraryOperationViewState.value = runSuspendCatching {
                try {
                    block()
                    LibraryOperationViewState(
                        state = LibraryOperationViewState.State.SUCCESS,
                        operation = operation,
                        watchFaceName = watchFace.name,
                    )
                } finally {
                    onComplete()
                }
            }.getOrElse { e ->
                Timber.w(e, "Failed to perform $operation")
                LibraryOperationViewState(
                    state = LibraryOperationViewState.State.ERROR,
                    operation = operation,
                    watchFaceName = watchFace.name,
                    errorEvent = ErrorEvent.get(e::class),
                )
            }

            delay(LIBRARY_OPERATION_TOAST_DURATION) // Show toast briefly
            _libraryOperationViewState.value = null
        }
    }

    private fun refresh(alwaysTriggerWatchSync: Boolean) {
        watchFace?.let {
            loadWatchFaceDetails(it)
            syncNow(alwaysTriggerWatchSync)
        }
    }

    companion object {
        private const val LIBRARY_OPERATION_TOAST_DURATION = 4000L
    }
}

package com.stt.android.device.watchface

import com.stt.android.data.source.local.DaoFactory
import com.stt.android.data.source.local.watchface.WatchFaceCapabilitiesDao
import com.stt.android.data.source.local.watchface.WatchFaceDao
import com.stt.android.data.source.local.watchface.WatchFaceStatusDao
import com.stt.android.data.source.local.watchface.WatchFaceSyncStateDao
import dagger.Module
import dagger.Provides

@Module
abstract class WatchFaceModule {

    /*companion object {

        @Provides
        fun provideWatchFaceDao(daoFactory: DaoFactory): WatchFaceDao =
            daoFactory.watchFaceDao

        @Provides
        fun provideWatchFaceStatusDao(daoFactory: DaoFactory): WatchFaceStatusDao =
            daoFactory.watchFaceStatusDao

        @Provides
        fun provideWatchFaceCapabilitiesDao(daoFactory: DaoFactory): WatchFaceCapabilitiesDao =
            daoFactory.watchFaceCapabilitiesDao

        @Provides
        fun provideWatchFaceSyncStateDao(daoFactory: DaoFactory): WatchFaceSyncStateDao =
            daoFactory.watchFaceSyncStateDao
    }*/
}

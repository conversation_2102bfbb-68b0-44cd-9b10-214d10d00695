package com.stt.android.device.remote.watchface

import android.content.Context
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.coroutines.runSuspendCatchingWithTimeout
import com.stt.android.device.domain.GetWatchCapabilitiesUseCase
import com.stt.android.watch.background.WatchFaceRemoteSyncJobLauncher
import com.stt.android.watch.watchface.SuuntoRunWatchFaceSyncProvider
import com.stt.android.worker.ifNotAlreadyScheduled
import com.suunto.connectivity.watchface.WatchFaceSyncLogicResult
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.minutes

class WatchFaceRemoteSyncJob(
    private val context: Context,
    private val params: WorkerParameters,
    private val remoteSyncLogic: WatchFaceRemoteSyncLogic,
    private val watchFaceSyncProvider: SuuntoRunWatchFaceSyncProvider,
    private val independentRemoteSyncLogic: WatchFaceIndependentRemoteSyncLogic,
    private val watchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
) : CoroutineWorker(context, params) {

    override suspend fun doWork(): Result {
        Timber.d("WatchFaceRemoteSyncJob doWork ====")
        val (serial, watchCapabilities) = watchCapabilitiesUseCase.getCurrentCapabilities()
        return if (serial == null || watchCapabilities == null || !watchCapabilities.isRunWatchFaceSupported) {
            syncRemoteWithoutWatch()
        } else {
            syncRemoteWithCompatibleWatch(serial)
        }
    }

    private suspend fun syncRemoteWithoutWatch(): Result =
        runSuspendCatching {
            independentRemoteSyncLogic.sync()
            Result.success()
        }.getOrElse { e ->
            Timber.w(e, "Watch independent remote sync failed")
            Result.failure()
        }

    private suspend fun syncRemoteWithCompatibleWatch(
        serial: String,
    ): Result {
        val alwaysTriggerWatchSync =
            params.inputData.getBoolean(KEY_ALWAYS_TRIGGER_WATCH_SYNC, false)
        Timber.d("syncRemoteWithCompatibleWatch alwaysTriggerWatchSync: $alwaysTriggerWatchSync")
        val result = remoteSyncLogic.syncWithBackend(serial)
        val triggerWatchSync = result.triggerWatchSync || alwaysTriggerWatchSync
        Timber.d("syncRemoteWithCompatibleWatch triggerWatchSync: $triggerWatchSync")
        if (triggerWatchSync) {
            runSuspendCatchingWithTimeout(5.minutes.inWholeMilliseconds) {
                watchFaceSyncProvider.sendSuuntoRunWatchFaceSyncQuery()
            }.onFailure { e ->
                Timber.w(e, "Unable to trigger suunto run watch face sync")
            }
        }

        return when (result) {
            is WatchFaceSyncLogicResult.Failure -> Result.failure()
            WatchFaceSyncLogicResult.NoNewData -> Result.success()
            is WatchFaceSyncLogicResult.Success -> Result.success()
        }
    }

    class Factory @Inject constructor(
        private val watchFaceRemoteSyncLogic: WatchFaceRemoteSyncLogic,
        private val independentRemoteSyncLogic: WatchFaceIndependentRemoteSyncLogic,
        private val watchFaceSyncProvider: SuuntoRunWatchFaceSyncProvider,
        private val watchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return WatchFaceRemoteSyncJob(
                context,
                params,
                remoteSyncLogic = watchFaceRemoteSyncLogic,
                independentRemoteSyncLogic = independentRemoteSyncLogic,
                watchFaceSyncProvider = watchFaceSyncProvider,
                watchCapabilitiesUseCase = watchCapabilitiesUseCase
            )
        }
    }

    companion object {
        private const val TAG = "WatchFaceRemoteSyncJob"
        private const val KEY_ALWAYS_TRIGGER_WATCH_SYNC = "KEY_ALWAYS_TRIGGER_WATCH_SYNC"

        @JvmStatic
        fun enqueue(
            workManager: WorkManager,
            alwaysTriggerWatchSync: Boolean = false,
        ) {
            workManager.ifNotAlreadyScheduled(TAG) {
                workManager.enqueueUniqueWork(
                    TAG,
                    ExistingWorkPolicy.APPEND_OR_REPLACE,
                    OneTimeWorkRequestBuilder<WatchFaceRemoteSyncJob>()
                        .apply {
                            // Don't require network if alwaysTriggerWatchSync is set. This allows the
                            // job to trigger the watch sync even if remote is won't succeed.
                            //
                            // If alwaysTriggerWatchSync is not set, then it makes no sense to run the
                            // job without a network connection.
                            if (!alwaysTriggerWatchSync) {
                                setConstraints(
                                    Constraints.Builder()
                                        .setRequiredNetworkType(NetworkType.CONNECTED)
                                        .build()
                                )
                            }
                        }
                        .setInputData(
                            Data.Builder()
                                .putBoolean(
                                    KEY_ALWAYS_TRIGGER_WATCH_SYNC,
                                    alwaysTriggerWatchSync
                                )
                                .build()
                        )
                        .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
                        .build()
                )
            }
        }
    }
}

class WatchFaceRemoteSyncJobLauncherImpl @Inject constructor(
    private val workManager: WorkManager,
) : WatchFaceRemoteSyncJobLauncher {
    override fun enqueueRemoteSyncJob(alwaysTriggerWatchSync: Boolean) {
        WatchFaceRemoteSyncJob.enqueue(workManager, alwaysTriggerWatchSync)
    }
}

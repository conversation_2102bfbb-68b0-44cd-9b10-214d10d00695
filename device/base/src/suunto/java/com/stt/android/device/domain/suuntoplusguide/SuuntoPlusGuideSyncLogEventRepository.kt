package com.stt.android.device.domain.suuntoplusguide

import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncEvent
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogEventDao
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject

class SuuntoPlusGuideSyncLogEventRepository @Inject constructor(
    private val syncLogEventDao: SuuntoPlusGuideSyncLogEventDao
) {
    fun getSyncStateFlow(): Flow<SuuntoPlusGuideSyncState> =
        syncLogEventDao.fetchLatestSyncEventAsFlow()
            .map {
                when (it?.event) {
                    SuuntoPlusGuideSyncEvent.WATCH_SYNC_STARTED -> SuuntoPlusGuideSyncState.WATCH_SYNC_ONGOING
                    SuuntoPlusGuideSyncEvent.BACKEND_SYNC_STARTED -> SuuntoPlusGuideSyncState.REMOTE_SYNC_ONGOING
                    SuuntoPlusGuideSyncEvent.IDLE,
                    SuuntoPlusGuideSyncEvent.BACKEND_SYNC_SUCCESS,
                    SuuntoPlusGuideSyncEvent.WATCH_SYNC_ERROR,
                    SuuntoPlusGuideSyncEvent.WATCH_SYNC_SUCCESS,
                    SuuntoPlusGuideSyncEvent.BACKEND_SYNC_ERROR,
                    null -> SuuntoPlusGuideSyncState.IDLE
                }
            }
            .catch {
                Timber.w(it, "Failed to listen for latest sync event")
                emit(SuuntoPlusGuideSyncState.IDLE)
            }
            .distinctUntilChanged()
}

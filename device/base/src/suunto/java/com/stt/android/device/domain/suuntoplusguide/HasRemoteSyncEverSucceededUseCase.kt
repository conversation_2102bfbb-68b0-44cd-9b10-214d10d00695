package com.stt.android.device.domain.suuntoplusguide

import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateRepository
import com.stt.android.device.datasource.WatchSerialDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class HasRemoteSyncEverSucceededUseCase
@Inject constructor(
    private val syncStateRepository: SuuntoPlusSyncStateRepository,
    private val currentWatchSerialDataSource: WatchSerialDataSource,
) {
    fun hasRemoteSyncEverSucceeded(): Flow<Boolean> =
        currentWatchSerialDataSource.getCurrentWatchSerialAsFlow()
            .flatMapLatest { serial ->
                if (serial != null) {
                    syncStateRepository.getPreviousRemoteSyncCapabilitiesAsFlow(serial)
                        .map { capabilities ->
                            capabilities?.capabilities?.isNotEmpty() == true
                        }
                } else {
                    flowOf(false)
                }
            }
}

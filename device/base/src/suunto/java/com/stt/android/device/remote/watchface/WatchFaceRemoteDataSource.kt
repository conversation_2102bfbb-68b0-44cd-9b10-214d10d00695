package com.stt.android.device.remote.watchface

import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.stt.android.device.domain.watchface.WatchFaceListContainer
import javax.inject.Inject

class WatchFaceRemoteDataSource @Inject constructor(
    private val onlineWatchFaceRemoteAPI: OnlineWatchFaceRemoteAPI,
) {
    suspend fun fetchAll(
        variantName: String,
        capabilities: String
    ): WatchFaceListContainer {
        return onlineWatchFaceRemoteAPI.fetchAll(variantName, capabilities).toDomain()
    }

    suspend fun getWatchFaceDetail(
        runFeatureCatalogueId: String,
        watchFaceCapabilities: String,
    ): WatchFaceEntity {
        return onlineWatchFaceRemoteAPI.getWatchFaceDetail(
            runFeatureCatalogueId,
            watchFaceCapabilities,
        ).toDomain()
    }

    suspend fun fetchWatchFaceFile(
        runFeatureCatalogueId: String,
        watchFaceCapabilities: String,
    ): ByteArray {
        return onlineWatchFaceRemoteAPI.fetchWatchFaceFile(
            runFeatureCatalogueId,
            watchFaceCapabilities
        )
    }

    suspend fun updateEnabledState(
        id: String,
        capabilities: String,
        addToFavorite: Boolean,
        addToWatch: Boolean,
    ) {
        addToLibrary(id, capabilities, addToFavorite, addToWatch)
    }

    suspend fun addToLibrary(
        id: String,
        watchFaceCapabilities: String,
        addToFavorite: Boolean,
        addToWatch: Boolean,
    ): Boolean {
        return onlineWatchFaceRemoteAPI.addToLibrary(
            id,
            watchFaceCapabilities,
            addToFavorite = addToFavorite,
            addToWatch = addToWatch
        )
    }

    suspend fun fetchUserLibrary(watchFaceCapabilities: String): List<WatchFace> {
        return onlineWatchFaceRemoteAPI.fetchUserLibrary(watchFaceCapabilities).toDomain()
    }
}

package com.stt.android.device.domain.suuntoplusguide

import com.stt.android.watch.ClearGuides
import com.stt.android.watch.ClearPlans
import com.stt.android.watch.ClearStoredWatchCapabilities
import com.stt.android.watch.ClearSuuntoPlusFeatures
import com.stt.android.watch.ClearWatchPluginStatus
import dagger.Binds
import dagger.Module

@Module
abstract class ClearSuuntoPlusDataModule {
    @Binds
    abstract fun bindClearStoredWatchCapabilities(
        clearStoredWatchCapabilitiesUseCase: ClearStoredWatchCapabilitiesUseCase
    ): ClearStoredWatchCapabilities

    @Binds
    abstract fun bindClearGuideDeviceStatus(
        clearGuideDeviceStatusUseCase: ClearWatchPluginStatusUseCase
    ): ClearWatchPluginStatus

    @Binds
    abstract fun bindClearGuidesAndCatalogue(
        clearGuidesAndCatalogueUseCase: ClearGuidesAndCatalogueUseCase
    ): ClearGuides

    @Binds
    abstract fun bindClearPlansAndCatalogue(
        clearPlansAndCatalogueUseCase: ClearPlansAndCatalogueUseCase
    ): ClearPlans

    @Binds
    abstract fun bindClearSuuntoPlusFeatures(
        clearSuuntoPlusFeaturesUseCase: ClearSuuntoPlusFeaturesUseCase
    ): ClearSuuntoPlusFeatures
}

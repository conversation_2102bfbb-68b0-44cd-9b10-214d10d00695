package com.stt.android.device.domain.suuntoplusguide

import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateRepository
import com.stt.android.device.datasource.WatchPluginStatusDataSource
import com.stt.android.device.datasource.WatchSerialDataSource
import com.stt.android.device.datasource.suuntoplusguide.SuuntoPlusGuidesLocalDataSource
import timber.log.Timber
import javax.inject.Inject

class InsertNewGuideOrUpdateExistingUseCase
@Inject constructor(
    private val syncStateRepository: SuuntoPlusSyncStateRepository,
    private val localGuideDataSource: SuuntoPlusGuidesLocalDataSource,
    private val pluginStatusDataSource: WatchPluginStatusDataSource,
    private val watchSerialDataSource: WatchSerialDataSource
) {
    suspend fun updateOrInsert(guide: SuuntoPlusGuide) {
        val serial = watchSerialDataSource.getCurrentWatchSerial()
        if (serial == null || !isSyncOngoing(serial)) {
            val existingGuide = localGuideDataSource.findById(guide.id)
            if (existingGuide != null) {
                // Keep local pinned state when updating
                localGuideDataSource.upsert(
                    guide.copy(pinned = existingGuide.pinned)
                )

                if (serial != null) {
                    val watchStatus = pluginStatusDataSource.getWatchStatus(
                        watchSerial = serial,
                        pluginId = guide.pluginId,
                    )

                    val watchStatusCapabilities =
                        pluginStatusDataSource.getCapabilitiesFromWatchState(
                            watchSerial = serial,
                            pluginId = guide.pluginId,
                        )

                    // If a guide is updated on the backend and it has IN_WATCH status,
                    // reset status to UNKNOWN. This will be updated to DOWNLOADING
                    // and hopefully DOWNLOADED during next remote sync.
                    if (watchStatus == SuuntoPlusPluginStatus.IN_WATCH && watchStatusCapabilities != null) {
                        pluginStatusDataSource.updateWatchStatus(
                            watchSerial = serial,
                            pluginId = guide.pluginId,
                            modifiedMillis = guide.modifiedMillis,
                            capabilities = watchStatusCapabilities,
                            status = SuuntoPlusPluginStatus.UNKNOWN,
                            interestValue = null,
                            type = SuuntoPlusPluginType.GUIDE,
                        )
                    }
                }
            } else {
                localGuideDataSource.upsert(guide)
            }
        } else {
            Timber.w("Sync ongoing: not updating guide ${guide.id}")
        }
    }

    private suspend fun isSyncOngoing(serial: String) =
        syncStateRepository.getSyncState(serial).isSyncOngoing
}

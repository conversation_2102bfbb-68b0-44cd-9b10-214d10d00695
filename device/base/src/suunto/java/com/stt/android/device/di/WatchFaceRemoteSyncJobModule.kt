package com.stt.android.device.di

import com.squareup.moshi.Moshi
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.backgroundwork.WorkerKey
import com.stt.android.data.source.local.watchface.WatchFaceSyncStateDao
import com.stt.android.data.source.local.watchface.WatchFaceSyncStateRepository
import com.stt.android.device.datasource.watchface.WatchFaceFileStorage
import com.stt.android.device.datasource.watchface.WatchFaceFileStorageImpl
import com.stt.android.device.remote.watchface.OnlineWatchFaceRemoteAPI
import com.stt.android.device.remote.watchface.OnlineWatchFaceRemoteAPIImpl
import com.stt.android.device.remote.watchface.OnlineWatchFaceRestAPI
import com.stt.android.device.remote.watchface.WatchFaceRemoteSyncJob
import com.stt.android.device.watch.WatchFaceSyncLogic
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory.getStOkHttpConfig
import com.stt.android.remote.di.RestApiFactory.buildRestApi
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.watch.watchface.SuuntoRunWatchFaceSyncProvider
import com.suunto.connectivity.watchface.WatchFaceMdsApi
import com.suunto.connectivity.watchface.WatchFaceSyncTrigger
import com.suunto.connectivity.watchface.WatchFaceWatchApi
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.multibindings.IntoMap
import okhttp3.OkHttpClient

@Module
abstract class WatchFaceRemoteSyncJobModule {

    @Binds
    @IntoMap
    @WorkerKey(WatchFaceRemoteSyncJob::class)
    abstract fun bindFactory(factory: WatchFaceRemoteSyncJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    abstract fun bindWatchFaceRemoteAPI(remoteAPI: OnlineWatchFaceRemoteAPIImpl): OnlineWatchFaceRemoteAPI

    @Binds
    abstract fun bindWatchFaceFileStorage(storage: WatchFaceFileStorageImpl): WatchFaceFileStorage

    @Binds
    abstract fun bindWatchFaceSyncTrigger(watchFaceSyncLogic: WatchFaceSyncLogic): WatchFaceSyncTrigger

    @Binds
    abstract fun bindWatchSyncProvider(suuntoWatchModel: SuuntoWatchModel): SuuntoRunWatchFaceSyncProvider

    @Binds
    abstract fun bindWatchFaceMdsAPI(
        watchFaceMdsApi: WatchFaceMdsApi
    ): WatchFaceWatchApi

    companion object {

        @Provides
        fun providerWatchFaceSyncStateRepository(
            syncStateDao: WatchFaceSyncStateDao
        ): WatchFaceSyncStateRepository {
            return WatchFaceSyncStateRepository(syncStateDao)
        }

        @Provides
        fun provideOnlineWatchFaceRestAPI(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): OnlineWatchFaceRestAPI {
            return buildRestApi(
                sharedClient,
                baseUrl,
                OnlineWatchFaceRestAPI::class.java,
                getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}

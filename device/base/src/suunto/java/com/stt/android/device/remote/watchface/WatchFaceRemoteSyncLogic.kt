package com.stt.android.device.remote.watchface

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.watchface.WatchFaceSyncLogUtil
import com.stt.android.data.source.local.watchface.WatchFaceSyncLogUtilImpl
import com.stt.android.data.source.local.watchface.WatchFaceSyncState
import com.stt.android.data.source.local.watchface.WatchFaceSyncStateRepository
import com.stt.android.device.datasource.watchface.WatchFaceFileStorage
import com.stt.android.device.datasource.watchface.WatchFaceLocalDataSource
import com.stt.android.device.datasource.watchface.WatchFaceStatusDataSource
import com.stt.android.device.datasource.watchface.toDomain
import com.stt.android.device.domain.watchface.GetWatchFaceCapabilitiesUseCase
import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.domain.watchface.WatchFaceStatus
import com.stt.android.exceptions.device.WatchPluginNotSupportedByCapabilities
import com.suunto.connectivity.watchface.WatchFaceSyncLogicResult
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject

class WatchFaceRemoteSyncLogic @Inject constructor(
    private val localDataSource: WatchFaceLocalDataSource,
    private val watchFaceStatusDataSource: WatchFaceStatusDataSource,
    private val remoteDataSource: WatchFaceRemoteDataSource,
    private val watchFaceStorage: WatchFaceFileStorage,
    private val syncStateRepository: WatchFaceSyncStateRepository,
    private val getWatchFaceCapabilitiesUseCase: GetWatchFaceCapabilitiesUseCase,
) : WatchFaceSyncLogUtil by WatchFaceSyncLogUtilImpl() {

    inner class InternalSyncState(
        val serial: String,
        val watchFaceCapabilities: List<String>,
        val errorsLogs: MutableList<String> = mutableListOf(),
        val triggerWatchSync: AtomicBoolean = AtomicBoolean(false),
        var remoteWatchFaces: List<WatchFace> = emptyList(),
        var localWatchFaces: List<WatchFace> = emptyList(),
        val watchFacesPendingDownload: MutableList<WatchFace> = mutableListOf(),
    ) {
        val remoteWatchFaceIds: List<String>
            get() = remoteWatchFaces.map { it.id }
        val localWatchFaceIds: List<String>
            get() = localWatchFaces.map { it.id }
        val localWatchFacesById: Map<String, WatchFace>
            get() = localWatchFaces.associateBy { it.id }
    }

    suspend fun syncWithBackend(serial: String): WatchFaceSyncLogicResult {
        Timber.d("WatchFaceRemoteSyncLogic syncWithBackend ====")
        return runSuspendCatching {
            try {
                val watchFaceCapabilities = getWatchFaceCapabilitiesUseCase()
                syncWithBackendInternal(serial, watchFaceCapabilities)
            } finally {
                withContext(NonCancellable) {
                    runSuspendCatching {
                        syncStateRepository.ensureIdle(serial)
                    }.onFailure { e ->
                        Timber.w(e, "Failed to set watch face sync status to idle")
                    }
                }
            }
        }.getOrElse { e ->
            Timber.w(e, "Failed to sync with backend.")
            processSyncErrorsAndStopSync(listOf(e.message.orEmpty()))
        }
    }

    private suspend fun processSyncErrorsAndStopSync(errorsLogs: List<String>) =
        processSyncErrorsAndStopSync(
            hasNewData = false,
            errorsLogs = errorsLogs,
            isWatchSync = false,
            triggerBackendSync = false,
            triggerWatchSync = false,
        )

    private suspend fun syncWithBackendInternal(
        serial: String,
        watchFaceCapabilities: List<String>
    ): WatchFaceSyncLogicResult = with(InternalSyncState(serial, watchFaceCapabilities)) {
        Timber.d("WatchFaceRemoteSyncLogic syncWithBackend111 ====")
        if (watchFaceCapabilities.isEmpty()) {
            Timber.d("watchFaceCapabilities is empty. Skip sync.")
            return WatchFaceSyncLogicResult.NoNewData
        }

        runSuspendCatching {
            syncStateRepository.ensureSyncStart(serial, WatchFaceSyncState.REMOTE_SYNC_ONGOING)

            remoteWatchFaces =
                remoteDataSource.fetchUserLibrary(watchFaceCapabilities.joinToString(","))
            localWatchFaces = localDataSource.listWatchFaces().first()

            // Locally delete watch faces removed from backend
            locallyDeleteWatchFacesRemovedFromBackend()

            // Insert new and update watch faces to database
            insertAndUpdateWatchFaces()

            // Get watch faces from database to fetch watch face files if needed
            localWatchFaces = localDataSource.listWatchFaces().first()

            // Set status to DOWNLOADED or DOWNLOADING depending on if watch face exists in cache
            updateDownloadPendingStateForWatchFaces()

            downloadWatchFaceFiles()

            if (!triggerWatchSync.get()) {
                triggerWatchSyncIfEnabledFlagsChanged()
            }
        }.onFailure { e ->
            val message = "watch face remote sync failed"
            Timber.w(e, message)
            errorsLogs.add("$message. ${e::class.java.name}: ${e.message}")
        }

        withContext(NonCancellable) {
            runSuspendCatching {
                syncStateRepository.ensureIdle(serial)
            }.onFailure { e ->
                val message = "Failed to update sync state to IDLE"
                Timber.w(e, message)
                errorsLogs.add("$message. ${e::class.java.name}: ${e.message}")
            }
        }

        return processSyncErrorsAndStopSync(
            hasNewData = true,
            errorsLogs = errorsLogs,
            isWatchSync = false,
            triggerBackendSync = false,
            triggerWatchSync = triggerWatchSync.get()
        )
    }

    private suspend fun InternalSyncState.insertAndUpdateWatchFaces() {
        for (remoteWatchFace in remoteWatchFaces) {
            val localWatchFace = localWatchFacesById[remoteWatchFace.id]
            val updateWatchFace = localWatchFace == null ||
                localWatchFace.addToWatch != remoteWatchFace.addToWatch ||
                localWatchFace.updated != remoteWatchFace.updated ||
                localWatchFace.supported != remoteWatchFace.supported
            if (updateWatchFace) {
                // todo: keep local enabled status when updating if local enabled flag
                localDataSource.upsert(remoteWatchFace)

                val id = remoteWatchFace.id
                val watchStatus = watchFaceStatusDataSource.getWatchFaceStatus(
                    watchSerial = serial,
                    id = id
                )

                // If a watch face is updated on the backend and it has IN_WATCH status,
                // reset status to UNKNOWN. This will be updated to DOWNLOADING
                // and hopefully DOWNLOADED while the remote sync proceeds.
                if (watchStatus == WatchFaceStatus.IN_WATCH) {
                    watchFaceStatusDataSource.updateWatchStatus(
                        watchSerial = serial,
                        id = id,
                        status = WatchFaceStatus.UNKNOWN,
                    )
                }
            }

            // todo: If local enable flag had changed, need push local enabled flag to remote
            /*if (localWatchFace != null && localWatchFace.addToWatch != remoteWatchFace.addToWatch) {
                runSuspendCatching {
                    remoteDataSource.updateEnabledState(
                        id = localWatchFace.id,
                        capabilities = watchFaceCapabilities.joinToString(","),
                        addToFavorite = localWatchFace.addToFavorite,
                        addToWatch = localWatchFace.addToWatch,
                    )
                }.onFailure { e ->
                    Timber.w(e, "Failed to update enabled flag to remote")
                    // Continue with sync even when updating enabled flag fails
                }
            }*/
        }
    }

    private suspend fun InternalSyncState.locallyDeleteWatchFacesRemovedFromBackend() {
        val deletedIds = localWatchFaceIds - remoteWatchFaceIds.toSet()
        if (deletedIds.any { getWatchFaceStatus(it) == WatchFaceStatus.IN_WATCH }) {
            // If any deleted WatchFace had IN_WATCH status, make sure to trigger watch sync
            triggerWatchSync.set(true)
        }
        localDataSource.deleteByIds(deletedIds)
    }

    private suspend fun InternalSyncState.updateDownloadPendingStateForWatchFaces() {
        for (watchFace in localWatchFaces) {
            val id = watchFace.id
            val watchFaceDeviceStatus = watchFaceStatusDataSource.getWatchFaceDeviceStatus(
                watchSerial = serial,
                id = id,
            )

            if (watchFaceDeviceStatus == null) {
                Timber.w("Watch face device status not found for $id")
                watchFacesPendingDownload.add(watchFace)
                continue
            }

            val installCapability = watchFaceDeviceStatus.installCapability
            val installVersion = watchFaceDeviceStatus.installVersion
            val watchStatus = watchFaceDeviceStatus.status.toDomain()
            if (watchFaceStorage.existsInCache(watchFace.watchfaceId, installCapability, installVersion)) {
                if (watchStatus == WatchFaceStatus.UNKNOWN || watchStatus == WatchFaceStatus.DOWNLOADING) {
                    watchFaceStatusDataSource.updateWatchStatus(
                        watchSerial = serial,
                        id = id,
                        status = WatchFaceStatus.DOWNLOADED,
                    )
                }
            } else if (watchStatus != WatchFaceStatus.NOT_SUPPORTED || watchFace.updated) {
                watchFaceStatusDataSource.updateWatchStatus(
                    watchSerial = serial,
                    id = id,
                    status = WatchFaceStatus.DOWNLOADING,
                )
                watchFacesPendingDownload.add(watchFace)
            }
        }
    }

    private suspend fun InternalSyncState.downloadWatchFaceFiles() {
        val watchCapabilities = watchFaceCapabilities.joinToString(",")
        for (watchFace in watchFacesPendingDownload) {
            runSuspendCatching {
                Timber.d("Downloading file for watch face=${watchFace.id}, capabilities=$watchFaceCapabilities")
                val watchFaceDetail = remoteDataSource.getWatchFaceDetail(
                    runFeatureCatalogueId = watchFace.id,
                    watchFaceCapabilities = watchCapabilities,
                )
                val data = remoteDataSource.fetchWatchFaceFile(
                    watchFaceDetail.runFeatureCatalogueId,
                    watchFaceDetail.targetInstallCapability
                )
                watchFaceStorage.store(
                    watchFaceId = watchFaceDetail.watchfaceId,
                    installCapability = watchFaceDetail.targetInstallCapability,
                    installVersion = watchFaceDetail.targetInstallVersion,
                    data
                )
                watchFaceStatusDataSource.updateWatchStatus(
                    watchSerial = serial,
                    id = watchFaceDetail.runFeatureCatalogueId,
                    watchFaceId = watchFaceDetail.watchfaceId,
                    installCapability = watchFaceDetail.targetInstallCapability,
                    installVersion = watchFaceDetail.targetInstallVersion,
                    watchFacePreImgName = watchFaceDetail.iconName,
                    fileSize = watchFaceDetail.fileSize,
                    fileMd5 = watchFaceDetail.md5,
                    status = WatchFaceStatus.DOWNLOADED,
                )

                triggerWatchSync.set(true)
            }.onFailure { e ->
                handleWatchFaceFileDownloadError(exception = e, id = watchFace.id)
            }
        }
    }

    private suspend fun InternalSyncState.handleWatchFaceFileDownloadError(
        exception: Throwable,
        id: String
    ) {
        val notSupported = exception is WatchPluginNotSupportedByCapabilities
        if (notSupported) {
            Timber.d("Failed to download watch face file for $id: Not supported by capabilities")
        } else {
            Timber.w(exception, "Failed to download watch face file for $id")
        }

        watchFaceStatusDataSource.updateWatchStatus(
            watchSerial = serial,
            id = id,
            status = if (notSupported) WatchFaceStatus.NOT_SUPPORTED else WatchFaceStatus.UNKNOWN,
        )
    }

    private suspend fun InternalSyncState.triggerWatchSyncIfEnabledFlagsChanged() {
        suspend fun WatchFace.getWatchStatus(): WatchFaceStatus? =
            getWatchFaceStatus(id)

        if (localWatchFaces.any { it.addToWatch && it.getWatchStatus() == WatchFaceStatus.DOWNLOADED }) {
            // Enabled watch face missing from watch -> need to run watch sync
            triggerWatchSync.set(true)
        }

        if (localWatchFaces.any { !it.addToWatch && it.getWatchStatus() == WatchFaceStatus.IN_WATCH }) {
            // Disabled watch face exists on watch -> need to run watch sync
            triggerWatchSync.set(true)
        }
    }

    private suspend fun InternalSyncState.getWatchFaceStatus(id: String) =
        watchFaceStatusDataSource.getWatchFaceStatus(watchSerial = serial, id = id)
}

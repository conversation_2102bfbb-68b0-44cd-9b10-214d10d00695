package com.stt.android.device.remote.watchface

import com.stt.android.watch.background.WatchFaceRemoteSyncJobLauncher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

interface DelayWatchFaceRemoteSyncTrigger {
    fun triggerSyncWithDelay(coroutineScope: CoroutineScope, alwaysTriggerWatchSync: Boolean)
    fun syncNow(alwaysTriggerWatchSync: Boolean)
    fun syncNowIfDelayedSyncRequested()
    fun clearDelayedSync()
    fun isDelayedSyncRequested(): <PERSON><PERSON><PERSON>
}

class DelayWatchFaceRemoteSyncTriggerImpl(
    private val launcher: WatchFaceRemoteSyncJobLauncher
) : DelayWatchFaceRemoteSyncTrigger {
    private var delayedSyncTriggerJob: Job? = null
    private var alwaysTriggerWatchSync = false

    override fun triggerSyncWithDelay(coroutineScope: CoroutineScope, alwaysTriggerWatchSync: Boolean) {
        this.alwaysTriggerWatchSync = alwaysTriggerWatchSync
        clearDelayedSync()
        delayedSyncTriggerJob = coroutineScope.launch {
            delay(DELAYED_SYNC_TIME_MILLIS)
            launcher.enqueueRemoteSyncJob(alwaysTriggerWatchSync)
        }
    }

    override fun syncNow(alwaysTriggerWatchSync: Boolean) {
        launcher.enqueueRemoteSyncJob(alwaysTriggerWatchSync)
    }

    override fun syncNowIfDelayedSyncRequested() {
        if (isDelayedSyncRequested()) {
            syncNow(alwaysTriggerWatchSync)
        }
    }

    override fun clearDelayedSync() {
        delayedSyncTriggerJob?.cancel()
        delayedSyncTriggerJob = null
    }

    override fun isDelayedSyncRequested() = delayedSyncTriggerJob?.isActive == true

    companion object {
        private const val DELAYED_SYNC_TIME_MILLIS = 30_000L
    }
}

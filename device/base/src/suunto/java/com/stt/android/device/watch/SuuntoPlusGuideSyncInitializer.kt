package com.stt.android.device.watch

import android.app.Application
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogEventDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateRepository
import com.stt.android.di.initializer.AppInitializer
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class SuuntoPlusGuideSyncInitializer
@Inject constructor(
    private val syncLogEventDao: SuuntoPlusGuideSyncLogEventDao,
    private val syncStateRepository: SuuntoPlusSyncStateRepository
) : AppInitializer {

    override fun init(app: Application) {
        GlobalScope.launch(IO) {
            runCatching {
                delay(300L)
                // The UI process only runs the remote sync so reset the sync state to IDLE only
                // if the state is REMOTE_SYNC_ONGOING. Leave it up to the connectivity process to handle
                // resetting WATCH_SYNC_ONGOING.
                syncLogEventDao.ensureIdleIfRemoteSyncOngoing("App start-up initialization")
                syncStateRepository.ensureRemoteSyncIdleForAll()
                syncLogEventDao.clearOldEntries()
            }.onFailure {
                Timber.w(it, "Error ensuring sync idle")
            }
        }
    }
}

package com.stt.android.data.source.local

import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.RoomDatabase
import com.stt.android.data.source.local.achievements.AchievementDao
import com.stt.android.data.source.local.achievements.LocalAchievement
import com.stt.android.data.source.local.billing.LocalPendingPurchase
import com.stt.android.data.source.local.billing.LocalSubscriptionInfo
import com.stt.android.data.source.local.billing.LocalSubscriptionItem
import com.stt.android.data.source.local.billing.PendingPurchaseDao
import com.stt.android.data.source.local.billing.SubscriptionInfoDao
import com.stt.android.data.source.local.billing.SubscriptionItemDao
import com.stt.android.data.source.local.diveextension.DiveExtensionDao
import com.stt.android.data.source.local.diveextension.LocalDiveExtension
import com.stt.android.data.source.local.fitnessextension.FitnessExtensionDao
import com.stt.android.data.source.local.fitnessextension.LocalFitnessExtension
import com.stt.android.data.source.local.gear.GearDao
import com.stt.android.data.source.local.gear.LocalGear
import com.stt.android.data.source.local.goaldefinition.GoalDefinitionDao
import com.stt.android.data.source.local.goaldefinition.LocalGoalDefinition
import com.stt.android.data.source.local.intensityextension.IntensityExtensionDao
import com.stt.android.data.source.local.intensityextension.LocalIntensityExtension
import com.stt.android.data.source.local.jumpropeextension.JumpRopeExtensionDao
import com.stt.android.data.source.local.jumpropeextension.LocalJumpRopeExtension
import com.stt.android.data.source.local.menstrualcycle.LocalMenstrualCycle
import com.stt.android.data.source.local.menstrualcycle.MenstrualCycleDao
import com.stt.android.data.source.local.notifications.LocalNotification
import com.stt.android.data.source.local.notifications.NotificationDao
import com.stt.android.data.source.local.offlinemusic.LocalPlaylist
import com.stt.android.data.source.local.offlinemusic.LocalPlaylistDetail
import com.stt.android.data.source.local.offlinemusic.LocalSongDetail
import com.stt.android.data.source.local.offlinemusic.PlaylistDao
import com.stt.android.data.source.local.offlinemusic.PlaylistDetailDao
import com.stt.android.data.source.local.offlinemusic.SongDetailDao
import com.stt.android.data.source.local.pois.LocalPOI
import com.stt.android.data.source.local.pois.LocalPOISyncLogEvent
import com.stt.android.data.source.local.pois.POIDao
import com.stt.android.data.source.local.pois.POISyncLogEventDao
import com.stt.android.data.source.local.ranking.LocalRanking
import com.stt.android.data.source.local.recovery.LocalRecoveryData
import com.stt.android.data.source.local.recovery.RecoveryDataDao
import com.stt.android.data.source.local.routes.LocalRoute
import com.stt.android.data.source.local.routes.RouteDao
import com.stt.android.data.source.local.routes.popular.LocalPopularRoute
import com.stt.android.data.source.local.routes.popular.PopularRouteDao
import com.stt.android.data.source.local.sleep.LocalSleepSegment
import com.stt.android.data.source.local.sleep.LocalSleepStageInterval
import com.stt.android.data.source.local.sleep.OldLocalSleep
import com.stt.android.data.source.local.sleep.SleepSegmentDao
import com.stt.android.data.source.local.sleep.SleepStagesDao
import com.stt.android.data.source.local.smlzip.LocalSMLZipReference
import com.stt.android.data.source.local.smlzip.SMLZipReferenceDao
import com.stt.android.data.source.local.summaryextension.LocalSummaryExtension
import com.stt.android.data.source.local.summaryextension.SummaryExtensionDao
import com.stt.android.data.source.local.suuntoplusfeature.LocalSportsAppSettingsState
import com.stt.android.data.source.local.suuntoplusfeature.LocalSuuntoPlusFeature
import com.stt.android.data.source.local.suuntoplusfeature.SportsAppSettingsStateDao
import com.stt.android.data.source.local.suuntoplusfeature.SuuntoPlusFeatureDao
import com.stt.android.data.source.local.suuntoplusguide.LocalSuuntoPlusGuide
import com.stt.android.data.source.local.suuntoplusguide.LocalSuuntoPlusGuideSyncLogEvent
import com.stt.android.data.source.local.suuntoplusguide.LocalSuuntoPlusPluginDeviceStatus
import com.stt.android.data.source.local.suuntoplusguide.LocalSuuntoPlusSyncState
import com.stt.android.data.source.local.suuntoplusguide.LocalTrainingPlan
import com.stt.android.data.source.local.suuntoplusguide.LocalWatchCapabilities
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogEventDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusPluginDeviceStatusDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateDao
import com.stt.android.data.source.local.suuntoplusguide.TrainingPlanDao
import com.stt.android.data.source.local.suuntoplusguide.WatchCapabilitiesDao
import com.stt.android.data.source.local.swimmingextension.LocalSwimmingExtension
import com.stt.android.data.source.local.swimmingextension.SwimmingExtensionDao
import com.stt.android.data.source.local.tags.LocalUserTag
import com.stt.android.data.source.local.tags.LocalUserTagWorkoutHeaderCrossRef
import com.stt.android.data.source.local.tags.UserTagsDao
import com.stt.android.data.source.local.trenddata.LocalTrendData
import com.stt.android.data.source.local.trenddata.LocalWeChatTrendData
import com.stt.android.data.source.local.trenddata.TrendDataDao
import com.stt.android.data.source.local.trenddata.WeChatTrendDataDao
import com.stt.android.data.source.local.user.LocalUser
import com.stt.android.data.source.local.user.UserDao
import com.stt.android.data.source.local.usercustomproperty.LocalUserCustomProperty
import com.stt.android.data.source.local.usercustomproperty.UserCustomPropertyDao
import com.stt.android.data.source.local.watchface.LocalWatchFace
import com.stt.android.data.source.local.watchface.LocalWatchFaceCapabilities
import com.stt.android.data.source.local.watchface.LocalWatchFaceDeviceStatus
import com.stt.android.data.source.local.watchface.LocalWatchFaceSyncState
import com.stt.android.data.source.local.watchface.WatchFaceCapabilitiesDao
import com.stt.android.data.source.local.watchface.WatchFaceDao
import com.stt.android.data.source.local.watchface.WatchFaceStatusDao
import com.stt.android.data.source.local.watchface.WatchFaceSyncStateDao
import com.stt.android.data.source.local.weatherextension.LocalWeatherExtension
import com.stt.android.data.source.local.weatherextension.WeatherExtensionDao
import com.stt.android.data.source.local.workout.LocalWeChatWorkoutData
import com.stt.android.data.source.local.workout.LocalWorkoutHeader
import com.stt.android.data.source.local.workout.WeChatWorkoutDataDao
import com.stt.android.data.source.local.workout.WorkoutHeaderDao
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributesUpdate
import com.stt.android.data.source.local.workout.attributes.WorkoutAttributesUpdateDao

const val DB_VERSION = 108

@Deprecated("Use TABLE_SLEEP_SEGMENTS")
const val TABLE_SLEEP = "sleep"
const val TABLE_SLEEP_SEGMENTS = "sleepsegments"
const val TABLE_TREND_DATA_OLD = "trenddata"
const val TABLE_TREND_DATA = "trenddata_v2"
const val TABLE_RECOVERY_DATA = "recoverydata"
const val TABLE_SUMMARY_EXTENSION = "summaryextension"
const val TABLE_DIVE_EXTENSION = "diveextension"
const val TABLE_SWIMMING_EXTENSION = "swimmingextension"
const val TABLE_WEATHER_EXTENSION = "weatherextension"
const val TABLE_SML_ZIP_REFERENCE = "smlzippreference"
const val TABLE_ROUTES = "routes"
const val TABLE_RANKINGS = "rankings"
const val TABLE_WORKOUT_ATTRIBUTES_UPDATE = "workoutAttributesUpdate"
const val TABLE_GOAL_DEFINITION = "goal_definitions"
const val TABLE_ACHIEVEMENTS = "achievements"
const val TABLE_POIS = "pois"
const val TABLE_POI_SYNC_LOG = "poi_sync_log"
const val TABLE_GEAR = "gear"
const val TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG = "suunto_plus_guide_sync_log"
const val TABLE_SUUNTO_PLUS_GUIDES = "suunto_plus_guides"
const val TABLE_TRAINING_PLANS = "suunto_plus_training_plans"
const val TABLE_INTENSITY_EXTENSION = "intensityextension"
const val TABLE_SLEEP_STAGE_INTERVALS = "sleep_stage_intervals"
const val TABLE_FITNESS_EXTENSION = "fitness_extension"
const val TABLE_NOTIFICATION = "notification"

@Deprecated("Dropped table")
const val TABLE_SUUNTO_PLUS_GUIDE_CATALOGUE = "suunto_plus_guide_catalogue"
const val TABLE_SUUNTO_PLUS_FEATURES = "suunto_plus_features"
const val TABLE_SUUNTO_PLUS_PLUGIN_DEVICE_STATUS = "suunto_plus_plugin_device_status"
const val TABLE_SUUNTO_PLUS_SYNC_STATE = "suunto_plus_sync_state"
const val TABLE_SUUNTO_PLUS_SPORTS_APP_SETTINGS_STATE = "suunto_plus_sports_app_settings_state"
const val TABLE_WATCH_CAPABILITIES = "watch_capabilities"
const val TABLE_USER = "user"
const val TABLE_WORKOUT_HEADERS = "workout_headers"
const val TABLE_USER_TAG = "user_tag"
const val TABLE_USER_TAG_WORKOUT_HEADERS_CROSS_REF = "user_tag_workout_headers_cross_ref"

const val TABLE_SUBSCRIPTION_INFO = "subscription_info"
const val TABLE_PENDING_PURCHASE = "pending_purchase"
const val TABLE_SUBSCRIPTION_ITEM = "subscription_item"
const val TABLE_USER_CUSTOM_PROPERTY = "user_custom_property"
const val TABLE_NEW_TREND_DATA = "new_trenddata"
const val TABLE_NEW_WORKOUT_DATA = "new_workoutdata"

const val TABLE_MENSTRUAL_CYCLE = "menstrual_cycle"
const val TABLE_PLAYLIST = "playlist"
const val TABLE_SONGS = "songs"
const val TABLE_PLAYLIST_DETAIL = "playlist_detail"
const val TABLE_JUMP_ROPE_EXTENSION = "jump_rope_extension"

const val TABLE_POPULAR_ROUTES = "popular_routes"

const val TABLE_WATCH_FACE = "watch_face"
const val TABLE_WATCH_FACE_DEVICE_STATUS = "watch_face_device_status"
const val TABLE_WATCH_FACE_CAPABILITIES = "watch_face_capabilities"
const val TABLE_WATCH_FACE_SYNC_STATE = "watch_face_sync_state"

@Database(
    entities = [
        OldLocalSleep::class,
        LocalSleepSegment::class,
        LocalTrendData::class,
        LocalRecoveryData::class,
        LocalSummaryExtension::class,
        LocalDiveExtension::class,
        LocalSMLZipReference::class,
        LocalSwimmingExtension::class,
        LocalRoute::class,
        LocalRanking::class,
        LocalWorkoutAttributesUpdate::class,
        LocalGoalDefinition::class,
        LocalWeatherExtension::class,
        LocalAchievement::class,
        LocalPOI::class,
        LocalPOISyncLogEvent::class,
        LocalGear::class,
        LocalSuuntoPlusGuide::class,
        LocalTrainingPlan::class,
        LocalSuuntoPlusFeature::class,
        LocalSuuntoPlusPluginDeviceStatus::class,
        LocalSuuntoPlusGuideSyncLogEvent::class,
        LocalSuuntoPlusSyncState::class,
        LocalSportsAppSettingsState::class,
        LocalWatchCapabilities::class,
        LocalUser::class,
        LocalWorkoutHeader::class,
        LocalUserTag::class,
        LocalUserTagWorkoutHeaderCrossRef::class,
        LocalSubscriptionInfo::class,
        LocalPendingPurchase::class,
        LocalSubscriptionItem::class,
        LocalWeChatTrendData::class,
        LocalWeChatWorkoutData::class,
        LocalIntensityExtension::class,
        LocalUserCustomProperty::class,
        LocalSleepStageInterval::class,
        LocalFitnessExtension::class,
        LocalMenstrualCycle::class,
        LocalPlaylist::class,
        LocalPlaylistDetail::class,
        LocalSongDetail::class,
        LocalJumpRopeExtension::class,
        LocalNotification::class,
        LocalPopularRoute::class,
        LocalWatchFace::class,
        LocalWatchFaceDeviceStatus::class,
        LocalWatchFaceCapabilities::class,
        LocalWatchFaceSyncState::class,
    ],
    version = DB_VERSION,
    autoMigrations = [
        AutoMigration(
            from = 46,
            to = 47 // Add LocalUserTag table
        ),
        AutoMigration(
            from = 47,
            to = 48 // Add 'ownerId' to LocalSuuntoPlusGuide
        ),
        AutoMigration(
            from = 48,
            to = 49 // Add 'isSynced' and 'isRemoved' to LocalUserTagWorkoutHeaderCrossRef
        ),
        AutoMigration(
            from = 49,
            to = 50,
            spec = AutoMigrationSpecs.AutoMigrationSpecFom49To50Spec::class // Rename id and key for LocalUserTag
        ),
        AutoMigration(
            from = 50,
            to = 51 // Add LocalSuuntoPlusSyncState
        ),
        AutoMigration(
            from = 51,
            to = 52 // Add LocalSuuntoPlusGuide.priorityIndex and LocalSuuntoPlusPluginDeviceStatus.fileSize
        ),
        AutoMigration(
            from = 52,
            to = 53 // Add LocalRouteProducer as embedded class to LocalRoute, and externalUrl for LocalRoute
        ),
        AutoMigration(
            from = 53,
            to = 54 // Modify column nullability for LocalGear
        ),
        AutoMigration(
            from = 54,
            to = 55 // Add LocalSuuntoPlusFeature: bannerUrl, ownerLogoUrl, richDescription and LocalSuuntoPlusGuide: richDescription
        ),
        AutoMigration(
            from = 55,
            to = 56 // Add index for userTagId in LocalUserTagWorkoutHeaderCrossRef to prevent unnecessary full table scans whenever parent table is modified
        ),
        AutoMigration(
            from = 56,
            to = 57 // add createdDate and lastLogin to LocalUser table
        ),
        AutoMigration(
            from = 57,
            to = 58 // Add LocalPendingPurchase, LocalSubscriptionInfo and LocalSubscriptionItem
        ),
        AutoMigration(
            from = 58,
            to = 59 // Add manifest JSON for SuuntoPlus™ features a.k.a. sports apps
        ),
        AutoMigration(
            from = 59,
            to = 60 // Add LocalSportsAppSettingsState
        ),
        AutoMigration(
            from = 60,
            to = 61 // Add LocalTrendData.hrv
        ),
        AutoMigration(
            from = 61,
            to = 62 // Add LocalSleepSegment.avgHrv and LocalSleepSegment.avgHrvSampleCount
        ),
        AutoMigration(
            from = 62,
            to = 63 // Add LocalIntensityExtension
        ),
        AutoMigration(
            from = 63,
            to = 64 // Add LocalUserCustomProperty
        ),
        AutoMigration(
            from = 64,
            to = 65 // add LocalWeChatTrendData and LocalWeChatWorkoutData
        ),
        AutoMigration(
            from = 65,
            to = 66 // add LocalSleepStageInterval and several fields to LocalSleepSegment
        ),
        AutoMigration(
            from = 66,
            to = 67 // add impactCardio and impactMuscular to LocalWorkoutHeader
        ),
        AutoMigration(
            from = 67,
            to = 68
        ),
        AutoMigration(
            from = 68,
            to = 69 // Add LocalFitnessExtension
        ),
        AutoMigration(
            from = 73,
            to = 74 // Add MenstrualCycle table
        ),
        AutoMigration(
            from = 74,
            to = 75,
            spec = AutoMigrationSpecs.AutoMigrationSpecFom74To75Spec::class
        ),
        AutoMigration(
            from = 75,
            to = 76 // Add diveTime to LocalDiveExtension
        ),
        AutoMigration(
            from = 76,
            to = 77 // Add roles list to LocalUser
        ),
        AutoMigration(
            from = 77,
            to = 78 // Add type to LocalSuuntoPlusFeature
        ),
        AutoMigration(
            from = 78,
            to = 79 // Add zoneSense to LocalWorkoutHeader
        ),
        AutoMigration(
            from = 79,
            to = 80 // Add LocalPlaylist LocalPlaylistDetail LocalSong tables
        ),
        AutoMigration(
            from = 80,
            to = 81 // Add breathingRate ,breaststrokeDuration... to LocalSwimmingExtension
        ),
        AutoMigration(
            from = 81,
            to = 82, // change LocalPlaylist and LocalPlaylistDetail primary key to playListId and add source type to them,
            // change LocalSongDetail primary key to key and update id to index,
            spec = AutoMigrationSpecs.AutoMigrationSpecFom81To82Spec::class
        ),
        AutoMigration(
            from = 82,
            to = 83, // add fields that stride,fatConsumption,carbohydrateConsumption to BackendWorkoutSummaryExtension
        ),
        AutoMigration(
            from = 85,
            to = 86, // add sourceType to LocalSongDetail
        ),
        AutoMigration(
            from = 86,
            to = 87, // Add lacticThHr to LocalSummaryExtension, add fitnessAge to LocalFitnessExtension
        ),
        AutoMigration(
            from = 87,
            to = 88 // add competition result to LocalSummaryExtension and LocalWorkoutHeader
        ),
        AutoMigration(
            from = 88,
            to = 89 // add avgAscentSpeed maxAscentSpeed avgDescentSpeed maxDescentSpeed avgDistancePerStroke to LocalSummaryExtension and LocalWorkoutHeader
        ),
        AutoMigration(
            from = 89,
            to = 90, // Add lacticThPace to LocalSummaryExtension
        ),
        AutoMigration(
            from = 90,
            to = 91, // Delete lacticThHr, lacticThPace from LocalSummaryExtension
            spec = AutoMigrationSpecs.AutoMigrationSpecFom90To91Spec::class
        ),
        AutoMigration(
            from = 91,
            to = 92, // Add lacticThHr, lacticThPace to LocalSummaryExtension (from Int to Float)
        ),
        AutoMigration(
            from = 92,
            to = 93, // remove competitionResult from LocalSummaryExtension and LocalWorkoutHeader
            spec = AutoMigrationSpecs.AutoMigrationSpecFom92To93Spec::class
        ),
        AutoMigration(
            from = 93,
            to = 94, // add breaststroke head angle to LocalSwimmingExtension
        ),
        AutoMigration(
            from = 94,
            to = 95, // add LocalJumpRopeExtension
        ),
        AutoMigration(
            from = 95,
            to = 96, // add LocalNotification
        ),
        AutoMigration(
            from = 96,
            to = 97, // add imageUrl to LocalNotification
        ),
        AutoMigration(
            from = 97,
            to = 98, // add type to LocalNotification
        ),
        AutoMigration(
            from = 98,
            to = 99, // update LocalJumpRopeExtension
        ),
        AutoMigration(
            from = 99,
            to = 100, // update LocalWeChatWorkoutData, add skipsCount field
        ),
        AutoMigration(
            from = 100,
            to = 101, // add LocalTrainingPlan
        ),
        AutoMigration(
            from = 101,
            to = 102, // update LocalUser, add coverImageUrl showLocale blocked fields
        ),
        AutoMigration(
            from = 102,
            to = 103, // add LocalPopularRoute
        ),
        AutoMigration(
            from = 103,
            to = 104, // update LocalTrainingPlan, add end Date
        ),
        AutoMigration(
            from = 104,
            to = 105, // update LocalSuuntoPlusGuide, LocalTrainingPlan add backgroundUrl
        ),
        AutoMigration(
            from = 105,
            to = 106, // update LocalTrainingPlan LocalSuuntoPlusGuide, add subTitle
        ),
        AutoMigration(
            from = 106,
            to = 107, // Add estimatedFloorsClimbed to LocalWorkoutHeader
        ),
        AutoMigration(
            from = 107,
            to = 108, // add LocalWatchFace LocalWatchFaceStatus, LocalWatchFaceCapabilities, LocalWatchFaceSyncState
        ),
    ]
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun sleepSegmentsDao(): SleepSegmentDao
    abstract fun trendDataDao(): TrendDataDao
    abstract fun recoveryDataDao(): RecoveryDataDao
    abstract fun summaryExtensionDao(): SummaryExtensionDao
    abstract fun diveExtensionDao(): DiveExtensionDao
    abstract fun smlZipReferenceDao(): SMLZipReferenceDao
    abstract fun swimmingExtensionDao(): SwimmingExtensionDao
    abstract fun routeDao(): RouteDao
    abstract fun rankingDao(): RankingDao
    abstract fun workoutAttributesUpdateDao(): WorkoutAttributesUpdateDao
    abstract fun goalDefinitionDao(): GoalDefinitionDao
    abstract fun weatherExtensionDao(): WeatherExtensionDao
    abstract fun achievementDao(): AchievementDao
    abstract fun poiDao(): POIDao
    abstract fun poiSyncLogEventDao(): POISyncLogEventDao
    abstract fun gearDao(): GearDao
    abstract fun suuntoPlusFeatureDao(): SuuntoPlusFeatureDao
    abstract fun suuntoPlusGuideDao(): SuuntoPlusGuideDao
    abstract fun trainingPlanDao(): TrainingPlanDao
    abstract fun suuntoPlusGuideSyncLogEventDao(): SuuntoPlusGuideSyncLogEventDao
    abstract fun suuntoPlusSyncStateDao(): SuuntoPlusSyncStateDao
    abstract fun sportsAppSettingsStateDao(): SportsAppSettingsStateDao
    abstract fun watchCapabilitiesDao(): WatchCapabilitiesDao
    abstract fun suuntoPlusPluginDeviceStatusDao(): SuuntoPlusPluginDeviceStatusDao
    abstract fun userDao(): UserDao
    abstract fun workoutHeaderDao(): WorkoutHeaderDao
    abstract fun userTagsDao(): UserTagsDao
    abstract fun subscriptionInfoDao(): SubscriptionInfoDao
    abstract fun subscriptionItemDao(): SubscriptionItemDao
    abstract fun pendingPurchaseDao(): PendingPurchaseDao
    abstract fun weChatTrendDataDao(): WeChatTrendDataDao
    abstract fun weChatWorkoutDataDao(): WeChatWorkoutDataDao
    abstract fun intensityExtensionDao(): IntensityExtensionDao
    abstract fun userCustomPropertyDao(): UserCustomPropertyDao
    abstract fun sleepStagesDao(): SleepStagesDao
    abstract fun fitnessExtensionDao(): FitnessExtensionDao
    abstract fun menstrualCyclePeriodDao(): MenstrualCycleDao
    abstract fun playlistDao(): PlaylistDao
    abstract fun playlistDetailDao(): PlaylistDetailDao
    abstract fun songDetailDao(): SongDetailDao
    abstract fun jumpRopeExtensionDao(): JumpRopeExtensionDao
    abstract fun notificationDao(): NotificationDao
    abstract fun popularRouteDao(): PopularRouteDao
    abstract fun watchFaceDao(): WatchFaceDao
    abstract fun watchFaceStatusDao(): WatchFaceStatusDao
    abstract fun watchFaceCapabilitiesDao(): WatchFaceCapabilitiesDao
    abstract fun watchFaceSyncStateDao(): WatchFaceSyncStateDao
}

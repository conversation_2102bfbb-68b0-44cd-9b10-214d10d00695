package com.stt.android.data.source.local.watchface

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.stt.android.data.source.local.TABLE_WATCH_FACE_DEVICE_STATUS
import timber.log.Timber
import kotlin.collections.forEach

@Dao
abstract class WatchFaceStatusDao {

    @Insert
    abstract suspend fun insert(watchFaceDeviceStatus: LocalWatchFaceDeviceStatus)

    @Update
    abstract suspend fun update(watchFaceDeviceStatus: LocalWatchFaceDeviceStatus)

    @Transaction
    open suspend fun updateStatus(
        watchSerial: String,
        id: String,
        status: LocalWatchFaceStatus
    ) {
        if (watchSerial.isBlank()) {
            throw IllegalArgumentException("Cannot update device status with blank serial")
        }

        val previousStatus = findWatchState(watchSerial, id)
        if (previousStatus == null) {
            // Do not throw an exception here as this should not be a serious enough issue to
            // fail the whole watch file sync.
            Timber.w("updateStatus: watch face status for watch $watchSerial and id $id not found")
        } else {
            Timber.d("Update existing watch face status, id=$id status=$status")
            update(previousStatus.copy(status = status))
        }
    }

    @Transaction
    open suspend fun updateStatus(
        watchSerial: String,
        ids: List<String>,
        status: LocalWatchFaceStatus
    ) {
        ids.forEach { updateStatus(watchSerial, it, status) }
    }

    @Transaction
    open suspend fun upsert(status: LocalWatchFaceDeviceStatus) {
        if (status.watchSerial.isBlank()) {
            throw IllegalArgumentException("Cannot add device status with blank serial")
        }
        val previousStatus = findWatchState(status.watchSerial, status.id)
        if (previousStatus == null) {
            Timber.d("Insert new watch face status, id=${status.id} status=${status.status}")
            insert(status)
        } else {
            Timber.d("Update existing watch face status, id=${status.id} status=${status.status}")
            // Keep previous fileSize instead of setting to null
            val fileSize = status.fileSize
            val fileMdf5 = status.fileMd5

            update(
                status.copy(fileSize = fileSize, fileMd5 = fileMdf5)
            )
        }
    }

    @Transaction
    open suspend fun upsert(statuses: List<LocalWatchFaceDeviceStatus>) {
        statuses.forEach { upsert(it) }
    }

    @Query(
        """SELECT *
         FROM $TABLE_WATCH_FACE_DEVICE_STATUS
         WHERE id = :id AND serial = :watchSerial"""
    )
    abstract suspend fun findWatchState(
        watchSerial: String,
        id: String,
    ): LocalWatchFaceDeviceStatus?

    @Query("DELETE FROM $TABLE_WATCH_FACE_DEVICE_STATUS WHERE `id` IN (:ids)")
    abstract suspend fun deleteByFeatureIds(ids: List<String>)
}

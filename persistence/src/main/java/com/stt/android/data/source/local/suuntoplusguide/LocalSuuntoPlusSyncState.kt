package com.stt.android.data.source.local.suuntoplusguide

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.squareup.moshi.Types
import com.stt.android.data.source.local.TABLE_SUUNTO_PLUS_SYNC_STATE
import com.stt.android.moshi.buildBasicMoshi
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities

@Entity(tableName = TABLE_SUUNTO_PLUS_SYNC_STATE)
@TypeConverters(NullableCapabilitiesConverter::class)
data class LocalSuuntoPlusSyncState(
    @PrimaryKey
    @ColumnInfo(name = WATCH_SERIAL)
    val watchSerial: String,
    @ColumnInfo(name = SYNC_STATE) val syncState: SuuntoPlusSyncState,
    @ColumnInfo(name = PREV_WATCH_SYNC_CAPABILITIES) val previousWatchSyncCapabilities: SuuntoWatchCapabilities?,
    @ColumnInfo(name = PREV_REMOTE_SYNC_CAPABILITIES) val previousRemoteSyncCapabilities: SuuntoWatchCapabilities?
) {
    companion object DbFields {
        const val WATCH_SERIAL = "serial"
        const val SYNC_STATE = "sync_state"
        const val PREV_WATCH_SYNC_CAPABILITIES = "prev_watch_sync_capabilities"
        const val PREV_REMOTE_SYNC_CAPABILITIES = "prev_remote_sync_capabilities"
    }
}

enum class SuuntoPlusSyncState {
    IDLE,
    REMOTE_SYNC_ONGOING,
    WATCH_SYNC_ONGOING;

    val isSyncOngoing: Boolean
        get() = this != IDLE
}

class NullableCapabilitiesConverter {
    private val moshi = buildBasicMoshi()
    private val stringListType = Types.newParameterizedType(List::class.java, String::class.java)
    private val stringListAdapter = moshi.adapter<List<String>>(stringListType)

    @TypeConverter
    fun fromJson(jsonString: String?): SuuntoWatchCapabilities? =
        jsonString?.let { SuuntoWatchCapabilities(stringListAdapter.fromJson(it)) }

    @TypeConverter
    fun toJson(capabilities: SuuntoWatchCapabilities?): String? =
        capabilities?.capabilities?.let { stringListAdapter.toJson(it) }
}

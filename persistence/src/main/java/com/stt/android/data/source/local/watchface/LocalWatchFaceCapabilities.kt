package com.stt.android.data.source.local.watchface

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.stt.android.data.source.local.StringListJsonConverter
import com.stt.android.data.source.local.TABLE_WATCH_FACE_CAPABILITIES

@Entity(tableName = TABLE_WATCH_FACE_CAPABILITIES)
@TypeConverters(StringListJsonConverter::class)
data class LocalWatchFaceCapabilities(
    @PrimaryKey
    @ColumnInfo(name = SERIAL)
    val serial: String,
    @ColumnInfo(name = MODEL) val model: String,
    @ColumnInfo(name = FW_VERSION) val fwVersion: String,
    @ColumnInfo(name = HW_VERSION) val hwVersion: String,
    @ColumnInfo(name = CAPABILITIES) val capabilities: List<String>
) {
    companion object DbFields {
        private const val SERIAL = "serial"
        private const val MODEL = "model"
        private const val FW_VERSION = "fw"
        private const val HW_VERSION = "hw"
        private const val CAPABILITIES = "capabilities"
    }
}

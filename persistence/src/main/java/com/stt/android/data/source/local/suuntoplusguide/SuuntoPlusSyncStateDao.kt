package com.stt.android.data.source.local.suuntoplusguide

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.TypeConverters
import com.stt.android.data.source.local.TABLE_SUUNTO_PLUS_SYNC_STATE
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

@Dao
@TypeConverters(NullableCapabilitiesConverter::class)
abstract class SuuntoPlusSyncStateDao {

    @Insert
    abstract suspend fun insert(syncState: LocalSuuntoPlusSyncState)

    @Query(
        """
        SELECT * FROM $TABLE_SUUNTO_PLUS_SYNC_STATE
        WHERE ${LocalSuuntoPlusSyncState.WATCH_SERIAL} = :serial
        """
    )
    abstract suspend fun getSyncState(serial: String): LocalSuuntoPlusSyncState?

    @Query(
        """
        SELECT * FROM $TABLE_SUUNTO_PLUS_SYNC_STATE
        WHERE ${LocalSuuntoPlusSyncState.WATCH_SERIAL} = :serial
        """
    )
    abstract fun getSyncStateAsFlow(serial: String): Flow<LocalSuuntoPlusSyncState?>

    @Query(
        """
        UPDATE $TABLE_SUUNTO_PLUS_SYNC_STATE
        SET ${LocalSuuntoPlusSyncState.SYNC_STATE} = :syncState
        WHERE ${LocalSuuntoPlusSyncState.WATCH_SERIAL} = :serial
        """
    )
    abstract suspend fun setSyncStateForSerial(serial: String, syncState: SuuntoPlusSyncState)

    @Query(
        """
        UPDATE $TABLE_SUUNTO_PLUS_SYNC_STATE
        SET ${LocalSuuntoPlusSyncState.SYNC_STATE} = :syncState
        """
    )
    abstract suspend fun setSyncStateForAll(syncState: SuuntoPlusSyncState)

    @Query(
        """
        UPDATE $TABLE_SUUNTO_PLUS_SYNC_STATE
        SET ${LocalSuuntoPlusSyncState.PREV_REMOTE_SYNC_CAPABILITIES} = :capabilities
        WHERE ${LocalSuuntoPlusSyncState.WATCH_SERIAL} = :serial
        """
    )
    abstract suspend fun updateRemoteSyncCapabilities(serial: String, capabilities: SuuntoWatchCapabilities)

    @Query(
        """
        UPDATE $TABLE_SUUNTO_PLUS_SYNC_STATE
        SET ${LocalSuuntoPlusSyncState.PREV_WATCH_SYNC_CAPABILITIES} = :capabilities
        WHERE ${LocalSuuntoPlusSyncState.WATCH_SERIAL} = :serial
        """
    )
    abstract suspend fun updateWatchSyncCapabilities(serial: String, capabilities: SuuntoWatchCapabilities)

    @Transaction
    open suspend fun setSyncState(
        serial: String,
        syncState: SuuntoPlusSyncState
    ) {
        if (getSyncState(serial) == null) {
            insert(
                LocalSuuntoPlusSyncState(
                    watchSerial = serial,
                    syncState = syncState,
                    previousRemoteSyncCapabilities = null,
                    previousWatchSyncCapabilities = null
                )
            )
        } else {
            setSyncStateForSerial(serial = serial, syncState = syncState)
        }
    }

    private suspend fun isSyncOngoing(serial: String) =
        getSyncState(serial).let {
            it != null && it.syncState != SuuntoPlusSyncState.IDLE
        }

    /**
     * Mark sync as ongoing. Throws if a sync is already running.
     * @param serial Watch serial
     * @param syncState Enum whether to mark watch sync or backend sync as ongoing
     */
    @Transaction
    open suspend fun ensureSyncStart(
        serial: String,
        syncState: SuuntoPlusSyncState
    ) {
        require(syncState == SuuntoPlusSyncState.REMOTE_SYNC_ONGOING || syncState == SuuntoPlusSyncState.WATCH_SYNC_ONGOING) {
            "syncState must be WATCH_SYNC_ONGOING or REMOTE_SYNC_ONGOING"
        }

        if (isSyncOngoing(serial)) {
            throw SuuntoPlusGuideSyncAlreadyRunningException()
        }

        setSyncState(
            serial = serial,
            syncState = syncState
        )

        Timber.d("Sync started ($serial): $syncState")
    }

    @Transaction
    open suspend fun ensureIdleForSerial(serial: String) {
        setSyncState(
            serial = serial,
            syncState = SuuntoPlusSyncState.IDLE
        )

        Timber.d("Sync ended ($serial)")
    }

    @Query(
        """
        UPDATE $TABLE_SUUNTO_PLUS_SYNC_STATE
        SET ${LocalSuuntoPlusSyncState.SYNC_STATE} = :newState
        WHERE ${LocalSuuntoPlusSyncState.SYNC_STATE} = :oldState
        """
    )
    abstract suspend fun updateSyncStateForAllInGivenState(
        oldState: SuuntoPlusSyncState,
        newState: SuuntoPlusSyncState
    ): Int

    open suspend fun ensureRemoteSyncIdleForAll() {
        val count = updateSyncStateForAllInGivenState(
            oldState = SuuntoPlusSyncState.REMOTE_SYNC_ONGOING,
            newState = SuuntoPlusSyncState.IDLE
        )

        if (count > 0) {
            Timber.w("Sync state forced from REMOTE_SYNC_ONGOING to IDLE (count=$count)")
        }
    }

    open suspend fun ensureWatchSyncIdleForAll() {
        val count = updateSyncStateForAllInGivenState(
            oldState = SuuntoPlusSyncState.WATCH_SYNC_ONGOING,
            newState = SuuntoPlusSyncState.IDLE
        )

        if (count > 0) {
            Timber.w("Sync state forced from WATCH_SYNC_ONGOING to IDLE (count=$count)")
        }
    }

    @Query(
        """
        DELETE FROM $TABLE_SUUNTO_PLUS_SYNC_STATE
        """
    )
    abstract suspend fun deleteAll()
}

package com.stt.android.data.source.local.watchface

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class WatchFaceSyncStateRepository @Inject constructor(
    private val syncStateDao: WatchFaceSyncStateDao,
) {
    fun getSyncStateAsFlow(serial: String): Flow<WatchFaceSyncState> =
        syncStateDao.getSyncStateAsFlow(serial)
            .map { it?.syncState ?: WatchFaceSyncState.IDLE }

    suspend fun ensureSyncStart(serial: String, syncState: WatchFaceSyncState) =
        syncStateDao.ensureSyncStart(serial, syncState)

    suspend fun ensureIdle(serial: String) =
        syncStateDao.ensureIdleForSerial(serial)

    /**
     * Ensure sync status is IDLE for all devices except keep REMOTE_SYNC_ONGOING state if set.
     */
    suspend fun ensureWatchSyncIdleForAll() =
        syncStateDao.ensureWatchSyncIdleForAll()

    /**
     * Ensure sync status is IDLE for all devices except keep WATCH_SYNC_ONGOING state if set.
     */
    suspend fun ensureRemoteSyncIdleForAll() =
        syncStateDao.ensureRemoteSyncIdleForAll()

    suspend fun clear() =
        syncStateDao.deleteAll()
}

package com.stt.android.data.source.local.watchface

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.stt.android.data.source.local.StringListJsonConverter
import com.stt.android.data.source.local.TABLE_WATCH_FACE

@Entity(tableName = TABLE_WATCH_FACE)
@TypeConverters(StringListJsonConverter::class)
data class LocalWatchFace(
    @PrimaryKey
    @ColumnInfo(name = ID)
    val id: String,
    @ColumnInfo(name = CATEGORY) val category: String,
    @ColumnInfo(name = TYPE) val type: String,
    @ColumnInfo(name = NAME) val name: String,
    @ColumnInfo(name = ICON_URL) val iconUrl: String?,
    @ColumnInfo(name = TILE_BANNER_URL) val tileBannerUrl: String?,
    @ColumnInfo(name = DESCRIPTION) val description: String,
    @ColumnInfo(name = SHORT_DESCRIPTION) val shortDescription: String?,
    @ColumnInfo(name = RICH_TEXT) val richText: String?,
    @ColumnInfo(name = LABELS) val labels: List<String>?,
    @ColumnInfo(name = USE_DEFAULT_IMAGES) val useDefaultImages: Boolean,
    @ColumnInfo(name = WATCH_FACE_ID) val watchfaceId: String,
    @ColumnInfo(name = CURRENT_VERSION) val currentVersion: String,
    @ColumnInfo(name = WATCH_CAPABILITY) val watchCapability: String,
    @ColumnInfo(name = ADD_TO_FAVORITE) val addToFavorite: Boolean,
    @ColumnInfo(name = ADD_TO_WATCH) val addToWatch: Boolean,
    @ColumnInfo(name = SUPPORTED) val supported: Boolean,
    @ColumnInfo(name = UPDATED) val updated: Boolean,
) {
    companion object DbFields {
        const val ID = "id"
        const val CATEGORY = "category"
        const val TYPE = "type"
        const val NAME = "name"
        const val ICON_URL = "iconUrl"
        const val TILE_BANNER_URL = "tileBannerUrl"
        const val DESCRIPTION = "description"
        const val SHORT_DESCRIPTION = "shortDescription"
        const val RICH_TEXT = "richText"
        const val LABELS = "labels"
        const val USE_DEFAULT_IMAGES = "useDefaultImages"
        const val WATCH_FACE_ID = "watchfaceId" // WatchFace ID for watch sync
        const val CURRENT_VERSION = "currentVersion"
        const val WATCH_CAPABILITY = "watchCapability"
        const val ADD_TO_FAVORITE = "addToFavorite"
        const val ADD_TO_WATCH = "addToWatch"
        const val SUPPORTED = "supported"
        const val UPDATED = "updated"
    }
}

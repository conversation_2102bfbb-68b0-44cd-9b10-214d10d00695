package com.stt.android.data.source.local.suuntoplusguide

import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class SuuntoPlusSyncStateRepository
@Inject constructor(
    private val syncStateDao: SuuntoPlusSyncStateDao
) {
    suspend fun updateRemoteSyncCapabilities(
        serial: String,
        capabilities: SuuntoWatchCapabilities
    ) = syncStateDao.updateRemoteSyncCapabilities(serial, capabilities)

    suspend fun getPreviousSyncCapabilities(serial: String): SuuntoSyncCapabilities? =
        syncStateDao.getSyncState(serial)
            ?.let { syncState ->
                SuuntoSyncCapabilities(
                    remoteSyncCapabilities = syncState.previousRemoteSyncCapabilities,
                    watchSyncCapabilities = syncState.previousWatchSyncCapabilities,
                )
            }

    fun getPreviousRemoteSyncCapabilitiesAsFlow(serial: String): Flow<SuuntoWatchCapabilities?> =
        syncStateDao.getSyncStateAsFlow(serial)
            .map { it?.previousRemoteSyncCapabilities }

    suspend fun updateWatchSyncCapabilities(serial: String, capabilities: SuuntoWatchCapabilities) =
        syncStateDao.updateWatchSyncCapabilities(serial, capabilities)

    fun getSyncStateAsFlow(serial: String): Flow<SuuntoPlusSyncState> =
        syncStateDao.getSyncStateAsFlow(serial)
            .map { it?.syncState ?: SuuntoPlusSyncState.IDLE }

    suspend fun getSyncState(serial: String): SuuntoPlusSyncState =
        getSyncStateAsFlow(serial).first()

    suspend fun ensureSyncStart(serial: String, syncState: SuuntoPlusSyncState) =
        syncStateDao.ensureSyncStart(serial, syncState)

    suspend fun ensureIdle(serial: String) =
        syncStateDao.ensureIdleForSerial(serial)

    /**
     * Ensure sync status is IDLE for all devices except keep REMOTE_SYNC_ONGOING state if set.
     */
    suspend fun ensureWatchSyncIdleForAll() =
        syncStateDao.ensureWatchSyncIdleForAll()

    /**
     * Ensure sync status is IDLE for all devices except keep WATCH_SYNC_ONGOING state if set.
     */
    suspend fun ensureRemoteSyncIdleForAll() =
        syncStateDao.ensureRemoteSyncIdleForAll()

    suspend fun clear() =
        syncStateDao.deleteAll()
}

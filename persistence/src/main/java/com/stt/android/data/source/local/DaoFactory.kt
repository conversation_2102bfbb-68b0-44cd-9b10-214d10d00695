package com.stt.android.data.source.local

import android.content.Context
import androidx.room.InvalidationTracker
import androidx.room.Room

class DaoFactory(context: Context, migrations: Migrations, isTesting: Boolean = false) {
    private val db: AppDatabase = Room.databaseBuilder(context, AppDatabase::class.java, DATABASE_FILE_NAME).apply {
        addMigrations(*migrations.list)
        if (isTesting) {
            allowMainThreadQueries()
        }
        // this is to allow the database to be accessed from multiple processes
        enableMultiInstanceInvalidation()
    }.build()

    val sleepSegmentsDao = db.sleepSegmentsDao()
    val trendDataDao = db.trendDataDao()
    val recoveryDataDao = db.recoveryDataDao()
    val diveExtensionDao = db.diveExtensionDao()
    val smlZipReferenceDao = db.smlZipReferenceDao()
    val swimmingReferenceDao = db.swimmingExtensionDao()
    val routeDao = db.routeDao()
    val rankingDao = db.rankingDao()
    val workoutAttributesUpdateDao = db.workoutAttributesUpdateDao()
    val goalDefinitionDao = db.goalDefinitionDao()
    val weatherExtensionDao = db.weatherExtensionDao()
    val achievementDao = db.achievementDao()
    val summaryExtensionDao = db.summaryExtensionDao()
    val poiDao = db.poiDao()
    val poiSyncLogEventDao = db.poiSyncLogEventDao()
    val gearDao = db.gearDao()
    val suuntoPlusFeatureDao = db.suuntoPlusFeatureDao()
    val suuntoPlusGuideDao = db.suuntoPlusGuideDao()
    val trainingPlanDao = db.trainingPlanDao()
    val suuntoPlusGuideSyncLogEventDao = db.suuntoPlusGuideSyncLogEventDao()
    val suuntoPlusSyncStateDao = db.suuntoPlusSyncStateDao()
    val suuntoPlusPluginDeviceStatusDao = db.suuntoPlusPluginDeviceStatusDao()
    val sportsAppSettingsStateDao = db.sportsAppSettingsStateDao()
    val watchCapabilitiesDao = db.watchCapabilitiesDao()
    val userDao = db.userDao()
    val workoutHeaderDao = db.workoutHeaderDao()
    val userTagsDao = db.userTagsDao()
    val subscriptionItemDao = db.subscriptionItemDao()
    val subscriptionInfoDao = db.subscriptionInfoDao()
    val pendingPurchaseDao = db.pendingPurchaseDao()
    val weChatTrendDataDao = db.weChatTrendDataDao()
    val weChatWorkoutDataDao = db.weChatWorkoutDataDao()
    val intensityExtensionDao = db.intensityExtensionDao()
    val userCustomPropertyDao = db.userCustomPropertyDao()
    val sleepStagesDao = db.sleepStagesDao()
    val fitnessExtensionDao = db.fitnessExtensionDao()
    val menstrualCycleDao = db.menstrualCyclePeriodDao()
    val playlistDao = db.playlistDao()
    val playDetailDao = db.playlistDetailDao()
    val songDetailDao = db.songDetailDao()
    val jumpRopeExtensionDao = db.jumpRopeExtensionDao()
    val notificationDao = db.notificationDao()
    val popularRouteDao = db.popularRouteDao()
    val watchFaceDao = db.watchFaceDao()
    val watchFaceStatusDao = db.watchFaceStatusDao()

    val watchFaceCapabilitiesDao = db.watchFaceCapabilitiesDao()
    val watchFaceSyncStateDao = db.watchFaceSyncStateDao()
    fun addObserver(observer: InvalidationTracker.Observer) {
        db.invalidationTracker.addObserver(observer)
    }

    fun removeObserver(observer: InvalidationTracker.Observer) {
        db.invalidationTracker.removeObserver(observer)
    }

    fun runInTransaction(block: () -> Unit) {
        db.runInTransaction(block)
    }

    companion object {
        const val DATABASE_FILE_NAME = "amer_app.db"
    }
}

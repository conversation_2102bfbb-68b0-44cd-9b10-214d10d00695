package com.stt.android.data.source.local.watchface

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.stt.android.data.source.local.TABLE_WATCH_FACE
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

@Dao
abstract class WatchFaceDao {

    @Transaction
    @Query("SELECT * FROM $TABLE_WATCH_FACE")
    abstract fun fetchAllWithStateAsFlow(): Flow<List<LocalWatchFaceWithDeviceStatus>>

    @Transaction
    @Query("SELECT * FROM $TABLE_WATCH_FACE WHERE id = :id")
    abstract fun findWatchFaceWithState(id: String): LocalWatchFaceWithDeviceStatus?

    @Transaction
    @Query("SELECT * FROM $TABLE_WATCH_FACE WHERE id = :id")
    abstract fun findWatchFaceWithStateAsFlow(id: String): Flow<LocalWatchFaceWithDeviceStatus?>

    @Query("SELECT * FROM $TABLE_WATCH_FACE")
    abstract fun fetchAllAsFlow(): Flow<List<LocalWatchFace>>

    @Query("SELECT * FROM $TABLE_WATCH_FACE WHERE id = :id")
    abstract suspend fun findById(id: String): LocalWatchFace?

    @Query("DELETE FROM $TABLE_WATCH_FACE WHERE id IN (:ids)")
    abstract suspend fun deleteByIds(ids: List<String>)

    @Query("UPDATE $TABLE_WATCH_FACE SET addToWatch = :addToWatch WHERE id = :id")
    abstract suspend fun updateAddToWatchFlag(id: String, addToWatch: Boolean): Int

    @Insert
    abstract suspend fun insert(watchFace: LocalWatchFace)

    @Update
    abstract suspend fun update(watchFace: LocalWatchFace)

    @Transaction
    open suspend fun upsert(watchFace: LocalWatchFace) {
        val oldWatchFace = findById(watchFace.id)
        if (oldWatchFace == null) {
            Timber.d("Insert new watch face, id=${watchFace.id}")
            insert(watchFace)
        } else {
            Timber.d("Update existing watch face, id=${watchFace.id}")
            update(watchFace)
        }
    }
}

package com.stt.android.data.source.local.suuntoplusguide

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.stt.android.data.source.local.TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import java.time.ZonedDateTime

@Dao
abstract class SuuntoPlusGuideSyncLogEventDao {

    @Query("SELECT * FROM $TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG")
    abstract suspend fun fetchAll(): List<LocalSuuntoPlusGuideSyncLogEvent>

    @Query("SELECT * FROM $TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG ORDER BY timestamp DESC LIMIT :limit")
    abstract suspend fun fetchMostRecent(limit: Int): List<LocalSuuntoPlusGuideSyncLogEvent>

    @Query(
        """
        SELECT * FROM $TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG
        ORDER BY `id` DESC LIMIT 1
        """
    )
    abstract suspend fun fetchLastSyncEvent(): LocalSuuntoPlusGuideSyncLogEvent?

    @Query(
        """
        SELECT * FROM $TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG
        ORDER BY `id` DESC LIMIT 1
        """
    )
    abstract fun fetchLatestSyncEventAsFlow(): Flow<LocalSuuntoPlusGuideSyncLogEvent?>

    @Insert
    abstract suspend fun insert(syncEvent: LocalSuuntoPlusGuideSyncLogEvent)

    @Update
    abstract suspend fun update(syncEvent: LocalSuuntoPlusGuideSyncLogEvent): Int

    @Query("DELETE FROM $TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG")
    abstract fun deleteAll()

    /**
     * Keeps a maximum of 500 rows in the DB
     */
    @Query(
        """
        DELETE FROM $TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG
        WHERE `id` NOT IN (
            SELECT `id`
            FROM $TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG
            ORDER BY `id` DESC
            LIMIT 500
        )
        """
    )
    abstract suspend fun clearOldEntries()

    /**
     * Log sync start event.
     * @param isWatchSync true if it is watch sync start, false if it is backend sync start
     */
    @Transaction
    open suspend fun logSyncStart(isWatchSync: Boolean) {
        val now = ZonedDateTime.now()
        val event = if (isWatchSync) {
            SuuntoPlusGuideSyncEvent.WATCH_SYNC_STARTED
        } else {
            SuuntoPlusGuideSyncEvent.BACKEND_SYNC_STARTED
        }
        insert(
            LocalSuuntoPlusGuideSyncLogEvent(
                timestampMillis = now.toInstant().toEpochMilli(),
                timeISO8601 = now,
                event = event,
                metadata = null,
            )
        )
        Timber.d("Logged SuuntoPlus Guide sync event: $event")
    }

    @Transaction
    open suspend fun ensureIdleIfRemoteSyncOngoing(reason: String) {
        val now = ZonedDateTime.now()
        if (fetchLastSyncEvent()?.event == SuuntoPlusGuideSyncEvent.BACKEND_SYNC_STARTED) {
            val message = "$reason: Forcing BACKEND_SYNC_STARTED -> IDLE"
            Timber.w(message)
            insert(
                LocalSuuntoPlusGuideSyncLogEvent(
                    timestampMillis = now.toInstant().toEpochMilli(),
                    timeISO8601 = now,
                    event = SuuntoPlusGuideSyncEvent.IDLE,
                    metadata = message,
                )
            )
        }
    }

    @Transaction
    open suspend fun ensureIdleIfWatchSyncOngoing(reason: String) {
        val now = ZonedDateTime.now()
        if (fetchLastSyncEvent()?.event == SuuntoPlusGuideSyncEvent.WATCH_SYNC_STARTED) {
            val message = "$reason: Forcing WATCH_SYNC_STARTED -> IDLE"
            Timber.w(message)
            insert(
                LocalSuuntoPlusGuideSyncLogEvent(
                    timestampMillis = now.toInstant().toEpochMilli(),
                    timeISO8601 = now,
                    event = SuuntoPlusGuideSyncEvent.IDLE,
                    metadata = message
                )
            )
        }
    }
}

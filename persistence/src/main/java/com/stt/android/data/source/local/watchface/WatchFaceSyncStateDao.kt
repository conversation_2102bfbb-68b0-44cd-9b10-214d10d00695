package com.stt.android.data.source.local.watchface

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import com.stt.android.data.source.local.TABLE_WATCH_FACE_SYNC_STATE
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

@Dao
abstract class WatchFaceSyncStateDao {

    @Insert
    abstract suspend fun insert(syncState: LocalWatchFaceSyncState)

    @Query(
        """
        SELECT * FROM $TABLE_WATCH_FACE_SYNC_STATE
        WHERE ${LocalWatchFaceSyncState.WATCH_SERIAL} = :serial
        """
    )
    abstract suspend fun getSyncState(serial: String): LocalWatchFaceSyncState?

    @Query(
        """
        SELECT * FROM $TABLE_WATCH_FACE_SYNC_STATE
        WHERE ${LocalWatchFaceSyncState.WATCH_SERIAL} = :serial
        """
    )
    abstract fun getSyncStateAsFlow(serial: String): Flow<LocalWatchFaceSyncState?>

    @Query(
        """
        UPDATE $TABLE_WATCH_FACE_SYNC_STATE
        SET ${LocalWatchFaceSyncState.SYNC_STATE} = :syncState
        WHERE ${LocalWatchFaceSyncState.WATCH_SERIAL} = :serial
        """
    )
    abstract suspend fun setSyncStateForSerial(serial: String, syncState: WatchFaceSyncState)

    @Query(
        """
        UPDATE $TABLE_WATCH_FACE_SYNC_STATE
        SET ${LocalWatchFaceSyncState.SYNC_STATE} = :syncState
        """
    )
    abstract suspend fun setSyncStateForAll(syncState: WatchFaceSyncState)

    @Transaction
    open suspend fun setSyncState(
        serial: String,
        syncState: WatchFaceSyncState
    ) {
        if (getSyncState(serial) == null) {
            insert(
                LocalWatchFaceSyncState(
                    watchSerial = serial,
                    syncState = syncState,
                )
            )
        } else {
            setSyncStateForSerial(serial = serial, syncState = syncState)
        }
    }

    private suspend fun isSyncOngoing(serial: String) =
        getSyncState(serial).let {
            it != null && it.syncState != WatchFaceSyncState.IDLE
        }

    /**
     * Mark sync as ongoing. Throws if a sync is already running.
     * @param serial Watch serial
     * @param syncState Enum whether to mark watch sync or backend sync as ongoing
     */
    @Transaction
    open suspend fun ensureSyncStart(
        serial: String,
        syncState: WatchFaceSyncState
    ) {
        require(syncState == WatchFaceSyncState.REMOTE_SYNC_ONGOING || syncState == WatchFaceSyncState.WATCH_SYNC_ONGOING) {
            "syncState must be WATCH_SYNC_ONGOING or REMOTE_SYNC_ONGOING"
        }

        if (isSyncOngoing(serial)) {
            throw WatchFaceSyncAlreadyRunningException()
        }

        setSyncState(
            serial = serial,
            syncState = syncState
        )

        Timber.d("Sync started ($serial): $syncState")
    }

    @Transaction
    open suspend fun ensureIdleForSerial(serial: String) {
        setSyncState(
            serial = serial,
            syncState = WatchFaceSyncState.IDLE
        )

        Timber.d("Sync ended ($serial)")
    }

    @Query(
        """
        UPDATE $TABLE_WATCH_FACE_SYNC_STATE
        SET ${LocalWatchFaceSyncState.SYNC_STATE} = :newState
        WHERE ${LocalWatchFaceSyncState.SYNC_STATE} = :oldState
        """
    )
    abstract suspend fun updateSyncStateForAllInGivenState(
        oldState: WatchFaceSyncState,
        newState: WatchFaceSyncState
    ): Int

    open suspend fun ensureRemoteSyncIdleForAll() {
        val count = updateSyncStateForAllInGivenState(
            oldState = WatchFaceSyncState.REMOTE_SYNC_ONGOING,
            newState = WatchFaceSyncState.IDLE
        )

        if (count > 0) {
            Timber.w("Sync state forced from REMOTE_SYNC_ONGOING to IDLE (count=$count)")
        }
    }

    open suspend fun ensureWatchSyncIdleForAll() {
        val count = updateSyncStateForAllInGivenState(
            oldState = WatchFaceSyncState.WATCH_SYNC_ONGOING,
            newState = WatchFaceSyncState.IDLE
        )

        if (count > 0) {
            Timber.w("Sync state forced from WATCH_SYNC_ONGOING to IDLE (count=$count)")
        }
    }

    @Query(
        """
        DELETE FROM $TABLE_WATCH_FACE_SYNC_STATE
        """
    )
    abstract suspend fun deleteAll()
}

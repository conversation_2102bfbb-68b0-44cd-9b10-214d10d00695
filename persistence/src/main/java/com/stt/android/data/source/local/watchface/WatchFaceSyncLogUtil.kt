package com.stt.android.data.source.local.watchface

import com.suunto.connectivity.watchface.WatchFaceSyncLogicResult
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.withContext

interface WatchFaceSyncLogUtil {
    suspend fun processSyncErrorsAndStopSync(
        hasNewData: <PERSON>olean,
        errorsLogs: List<String>,
        isWatchSync: <PERSON><PERSON><PERSON>,
        triggerBackendSync: <PERSON><PERSON><PERSON>,
        triggerWatchSync: <PERSON>olean
    ): WatchFaceSyncLogicResult
}

class WatchFaceSyncLogUtilImpl(
) : WatchFaceSyncLogUtil {
    override suspend fun processSyncErrorsAndStopSync(
        hasNewData: <PERSON>olean,
        errorsLogs: List<String>,
        isWatchSync: <PERSON><PERSON><PERSON>,
        triggerBackendSync: <PERSON>olean,
        triggerWatchSync: <PERSON>olean
    ): WatchFaceSyncLogicResult = withContext(NonCancellable) {
        val errorLog = errorsLogs.joinToString("\n").let {
            it.substring(0, it.length.coerceAtMost(1000))
        }.trim()
        when {
            errorLog.isNotEmpty() -> WatchFaceSyncLogicResult.Failure(
                message = errorLog,
                triggerBackendSync = triggerBackendSync,
                triggerWatchSync = triggerWatchSync
            )

            hasNewData -> WatchFaceSyncLogicResult.Success(
                triggerBackendSync = triggerBackendSync,
                triggerWatchSync = triggerWatchSync
            )

            else -> WatchFaceSyncLogicResult.NoNewData
        }
    }
}

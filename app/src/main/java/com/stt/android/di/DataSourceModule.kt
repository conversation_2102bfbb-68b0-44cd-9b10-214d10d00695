package com.stt.android.di

import com.stt.android.data.pois.POIRemoteSyncJobModule
import com.stt.android.data.servicestatus.ServerStatusModule
import com.stt.android.data.user.follow.FollowDataSourceModule
import com.stt.android.data.user.followees.FolloweeDataSourceModule
import com.stt.android.datasource.explore.di.POIDataSourceModule
import com.stt.android.di.datasource.BrandDataSourceModule
import com.stt.android.di.datasource.WorkoutDataSourceModule
import com.stt.android.di.gear.GearModule
import com.stt.android.di.goaldefinition.GoalDefinitionModule
import com.stt.android.di.marketing.MarketingDataSourceModule
import com.stt.android.di.marketingconsent.MarketingConsentDataSourceModule
import com.stt.android.di.moshiadapters.MoshiAdaptersModule
import com.stt.android.di.notifications.NotificationsDataSourceModule
import com.stt.android.di.rankings.RankingsModule
import com.stt.android.di.report.ReportModule
import com.stt.android.di.sml.SmlModule
import com.stt.android.di.smlzip.SMLZipModule
import com.stt.android.domain.user.UserDataSourceModule
import com.stt.android.domain.user.customproperty.UserCustomPropertyModule
import com.stt.android.home.explore.routes.RouteModule
import com.stt.android.home.people.FolloweeDaoModule
import com.stt.android.menstrualcycle.datasource.MenstrualCycleDataSourceModule
import com.stt.android.notifications.FcmTokenManagerModule
import com.stt.android.usersettings.UserSettingsModule
import com.stt.android.workouts.achievements.AchievementsModule
import com.stt.android.workouts.pictures.PicturesModule
import com.stt.android.workouts.reaction.ReactionDataSourceModule
import com.stt.android.workouts.videos.VideosModule
import dagger.Module

@Module(
    includes = [
        WorkoutDataSourceModule::class,
        ExtensionDataAccessModule::class,
        RouteModule::class,
        SmlModule::class,
        SMLZipModule::class,
        RankingsModule::class,
        GearModule::class,
        GoalDefinitionModule::class,
        UserSettingsModule::class,
        FcmTokenManagerModule::class,
        ReactionDataSourceModule::class,
        PicturesModule::class,
        VideosModule::class,
        MoshiAdaptersModule::class,
        ReportModule::class,
        UserDataSourceModule::class,
        FolloweeDaoModule::class,
        FolloweeDataSourceModule::class,
        FollowDataSourceModule::class,
        ServerStatusModule::class,
        MarketingDataSourceModule::class,
        MarketingConsentDataSourceModule::class,
        AchievementsModule::class,
        POIDataSourceModule::class,
        POIRemoteSyncJobModule::class,
        UserCustomPropertyModule::class,
        MenstrualCycleDataSourceModule::class,
        NotificationsDataSourceModule::class,
    ]
)
abstract class DataSourceModule : BrandDataSourceModule()

package com.stt.android.di.job

import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.backgroundwork.WorkerKey
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusGuideRemoteSyncJobLauncherImpl
import com.stt.android.device.remote.watchface.WatchFaceRemoteSyncJobLauncherImpl
import com.stt.android.device.watch.SuuntoPlusGuideSyncInitializer
import com.stt.android.device.watch.WatchFaceSyncInitializer
import com.stt.android.di.initializer.AppInitializer
import com.stt.android.home.diary.analytics.Daily247AnalyticsJobModule
import com.stt.android.home.explore.routes.RouteSyncProviderModule
import com.stt.android.offlinemaps.domain.DownloadOfflineMapWorker
import com.stt.android.watch.background.AndroidBroadcastDispatcher
import com.stt.android.watch.background.BroadcastDispatcher
import com.stt.android.watch.background.ConvertLogBookJob
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import com.stt.android.watch.background.UpdateSettingsJob
import com.stt.android.watch.background.WatchFaceRemoteSyncJobLauncher
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap
import dagger.multibindings.IntoSet

@Module(
    includes = [
        Daily247AnalyticsJobModule::class,
        RouteSyncProviderModule::class
    ]
)
abstract class BrandJobModule : BaseJobModule() {
    @Binds
    @IntoMap
    @WorkerKey(ConvertLogBookJob::class)
    abstract fun bindConvertLogBookJob(convertLogBookJobFactory: ConvertLogBookJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @IntoMap
    @WorkerKey(UpdateSettingsJob::class)
    abstract fun bindUpdateSettingsJob(updateSettingsJobFactory: UpdateSettingsJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    abstract fun bindAndroidBroadcastDispatcher(broadcastDispatcher: AndroidBroadcastDispatcher): BroadcastDispatcher

    @Binds
    abstract fun bindSuuntoPlusGuideRemoteSyncJobLauncher(SuuntoPlusGuideRemoteSyncJobLauncherImpl: SuuntoPlusGuideRemoteSyncJobLauncherImpl): SuuntoPlusGuideRemoteSyncJobLauncher

    @Binds
    @IntoSet
    abstract fun bindSuuntoPlusGuideSyncInitializer(suuntoPlusGuideSyncInitializer: SuuntoPlusGuideSyncInitializer): AppInitializer

    @Binds
    @IntoMap
    @WorkerKey(DownloadOfflineMapWorker::class)
    abstract fun bindDownloadOfflineMapWorker(downloadOfflineMapWorker: DownloadOfflineMapWorker.Factory): CoroutineWorkerAssistedFactory

    @Binds
    abstract fun bindWatchFaceRemoteSyncJobLauncher(watchLaceRemoteSyncJobLauncherImpl: WatchFaceRemoteSyncJobLauncherImpl): WatchFaceRemoteSyncJobLauncher

    @Binds
    abstract fun bindWatchFaceSyncInitializer(watchFaceSYncInitializer: WatchFaceSyncInitializer): AppInitializer
}

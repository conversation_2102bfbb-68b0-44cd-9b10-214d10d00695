package com.stt.android.home.explore.routes.popular

import android.content.Context
import android.view.ViewGroup
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.viewinterop.AndroidView
import com.soy.algorithms.ascent.VerticalDelta
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.routes.TopRoute
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.routes.RouteAltitudeChartData
import com.stt.android.home.explore.routes.RouteUtils
import com.stt.android.home.explore.routes.RouteValueFormatHelper
import com.stt.android.home.explore.routes.details.ClimbGuidanceSegmentData
import com.stt.android.home.explore.routes.details.RouteDetailSummary
import com.stt.android.home.explore.routes.details.RouteHighlightData
import com.stt.android.home.explore.routes.ui.ClimbGuidanceAwareRouteAltitudeChartWithAxis
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workoutsettings.follow.RouteCard
import com.stt.android.core.R as CR

@Composable
fun PopularRouteBottomScreen(
    viewmodel: BaseTopRouteDetailViewModel,
    distanceFromCurrentPosition: Double,
    infoModelFormatter: InfoModelFormatter,
    measurementUnit: MeasurementUnit,
    modifier: Modifier = Modifier,
) {
    val viewData = viewmodel.routeDetailsViewData.collectAsState().value ?: return
    val summaryUpdate = viewmodel.routeSummaryUpdate.collectAsState().value
    val route = remember { viewData.route }
    var routeHighlightData by remember { mutableStateOf<RouteHighlightData?>(null) }

    LaunchedEffect(routeHighlightData) {
        viewmodel.updateByRouteHighlightDataChanged(routeHighlightData)
    }

    Column(
        modifier = modifier.fillMaxSize()
    ) {
        PopularRouteBottomHeader(
            route = route,
            distanceFromCurrentPosition = distanceFromCurrentPosition,
            measurementUnit = measurementUnit
        )

        HorizontalDivider(color = MaterialTheme.colorScheme.lightGrey)

        PopularRouteBottomSummary(
            route = route,
            summaryUpdate = summaryUpdate,
            infoModelFormatter = infoModelFormatter
        )

        PopularRouteSessionAndChart(
            data = viewData.routeAltitudeChartData,
            climbGuidance = viewData.climbGuidance,
            infoModelFormatter = infoModelFormatter,
            onValueHighlighted = { highlightData ->
                routeHighlightData = highlightData
            }
        )

        AddToWatchView(viewData = viewData)

        PopularRouteLocation(
            startPoint = route.startPoint,
            stopPoint = route.stopPoint,
            startAddress = route.getStartAddress() ?: "",
            stopAddress = route.getEndAddress() ?: ""
        )
    }
}

@Composable
private fun PopularRouteBottomHeader(
    route: TopRoute,
    distanceFromCurrentPosition: Double,
    measurementUnit: MeasurementUnit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val activityType = remember { ActivityType.valueOf(route.activityId) }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium)
    ) {
        SuuntoActivityIcon(
            iconSize = MaterialTheme.iconSizes.medium,
            iconRes = activityType.iconId,
            tint = MaterialTheme.colorScheme.surface,
            background = colorResource(activityType.colorId)
        )

        Spacer(Modifier.width(MaterialTheme.spacing.medium))

        Column(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
        ) {
            Text(
                text = route.getTopRouteName(),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.bodyLargeBold,
            )

            Text(
                text = getSubTitleString(context, distanceFromCurrentPosition, measurementUnit),
                color = MaterialTheme.colorScheme.secondary,
                style = MaterialTheme.typography.bodyMedium,
            )
        }
    }
}

private fun getSubTitleString(
    context: Context,
    distanceFromCurrentPosition: Double,
    measurementUnit: MeasurementUnit
): String {
    if (distanceFromCurrentPosition == RouteCard.DISTANCE_NOT_SET) {
        return ""
    }
    return RouteUtils.buildRouteSubtitle(
        distanceFromCurrentLocation = distanceFromCurrentPosition,
        producerName = "",
        measurementUnit = measurementUnit,
        context = context
    )
}

@Composable
private fun PopularRouteBottomSummary(
    route: TopRoute,
    summaryUpdate: RouteDetailSummary?,
    infoModelFormatter: InfoModelFormatter,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val valueStyle = MaterialTheme.typography.bodyXLargeBold
    val unitStyle = MaterialTheme.typography.bodySmall
    val formatterHelper =
        remember { RouteValueFormatHelper(context, infoModelFormatter) }

    val (distance, duration, verticalDelta) = if (summaryUpdate == null) {
        Triple(
            route.getTotalDistanceFromMetaData(),
            route.getDurationEstimationFromMetaData(),
            VerticalDelta(route.getAscentFromMetaData(), route.getDescentFromMetaData())
        )
    } else {
        Triple(summaryUpdate.distance, summaryUpdate.duration, summaryUpdate.verticalDelta)
    }

    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium)
    ) {
        RouteSummaryItem(
            horizontalAlignment = Alignment.Start,
            label = stringResource(CR.string.summary_item_title_distance),
            value = formatterHelper.formatValueAndUnitAsAnnotatedString(
                SummaryItem.DISTANCE,
                distance,
                valueStyle,
                unitStyle
            )
        )
        RouteSummaryItem(
            label = stringResource(CR.string.summary_item_title_ascent),
            value = formatterHelper.formatValueAndUnitAsAnnotatedString(
                SummaryItem.ASCENTALTITUDE,
                verticalDelta?.ascent ?: 0.0,
                valueStyle,
                unitStyle
            )
        )
        RouteSummaryItem(
            label = stringResource(CR.string.summary_item_title_descent),
            value = formatterHelper.formatValueAndUnitAsAnnotatedString(
                SummaryItem.DESCENTALTITUDE,
                verticalDelta?.descent ?: 0.0,
                valueStyle,
                unitStyle
            )
        )
        RouteSummaryItem(
            horizontalAlignment = Alignment.End,
            label = formatterHelper.formatValueAndUnitAsPlainString(
                SummaryItem.AVGSPEED,
                route.getAverageSpeedFromMetaData()
            ),
            value = AnnotatedString(
                text = infoModelFormatter.formatEstimatedRouteDuration(duration)
            )
        )
    }
}

@Composable
private fun PopularRouteSessionAndChart(
    data: RouteAltitudeChartData,
    climbGuidance: ClimbGuidance,
    onValueHighlighted: (RouteHighlightData) -> Unit,
    infoModelFormatter: InfoModelFormatter,
    modifier: Modifier = Modifier,
) {
    val climbGuidanceSegmentData = remember(climbGuidance) {
        mutableStateOf(
            ClimbGuidanceSegmentData(
                showClimbGuidanceSegmentOverview = true,
                climbGuidance = climbGuidance
            )
        )
    }

    Column(modifier = modifier) {
        ClimbGuidanceSegmentScreen(
            climbSegmentData = climbGuidanceSegmentData.value,
            infoModelFormatter = infoModelFormatter
        )

        AndroidView(
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
            factory = { context ->
                ClimbGuidanceAwareRouteAltitudeChartWithAxis(context).apply {
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                    )
                }
            },
            update = { climbGuidanceChart ->
                climbGuidanceChart.updateRouteAltitudeChart(data, climbGuidance) { currentX, _ ->
                    if (currentX > 0) {
                        climbGuidanceChart.moveHighlightToX(currentX.toFloat(), climbGuidance)
                    } else {
                        climbGuidanceChart.moveHighlightToEnd(climbGuidance)
                    }

                    climbGuidanceSegmentData.value = ClimbGuidanceSegmentData(
                        showClimbGuidanceSegmentOverview = climbGuidanceChart.isDraggedToEnd(),
                        climbGuidance = climbGuidance,
                        currentClimbSegment = climbGuidanceChart.getCurrentClimbSegment()
                    )

                    val routeHighlightData = RouteHighlightData(
                        chartData = data,
                        climbGuidance = climbGuidance,
                        index = currentX
                    )
                    onValueHighlighted.invoke(routeHighlightData)
                }
            }
        )
    }
}

package com.stt.android.home.explore.routes.details

import android.os.Bundle
import android.view.View
import android.widget.CompoundButton
import androidx.activity.viewModels
import androidx.core.view.isVisible
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.soy.algorithms.climbanalysis.entities.ClimbSegment
import com.stt.android.common.ui.avalanchemap.AvalancheInfoHelper
import com.stt.android.common.ui.avalanchemap.AvalancheInfoPopupFragment
import com.stt.android.common.ui.avalanchemap.AvalancheLegend
import com.stt.android.common.ui.observeNotNull
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.domain.routes.Route
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.routes.RouteAltitudeChartData
import com.stt.android.home.explore.routes.addtowatch.AddRouteToWatchViewModel
import com.stt.android.home.explore.routes.ui.ClimbGuidanceSegment
import com.stt.android.home.explore.routes.ui.ClimbGuidanceSegmentOverview
import com.stt.android.maps.MapType
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.isAvalancheMap
import com.stt.android.ui.map.SelectedMapTypeLiveData
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class RouteDetailsActivity : BaseRouteDetailsActivity() {

    @Inject
    lateinit var selectedMapTypeLiveData: SelectedMapTypeLiveData

    @Inject
    lateinit var avalancheInfoHelper: AvalancheInfoHelper

    private val addRouteToWatchViewModel by viewModels<AddRouteToWatchViewModel>()

    private val onCheckedChangeListener = CompoundButton.OnCheckedChangeListener { _, isChecked ->
        routeDetailsPresenter.onAddToWatchEnabled(isChecked)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.routeDetailMainContent.avalancheInfo.setContentWithTheme {
            AvalancheLegend(onClick = {
                AvalancheInfoPopupFragment.newInstance().show(supportFragmentManager, null)
            })
        }
    }

    override fun onMapReady(map: SuuntoMap) {
        super.onMapReady(map)
        selectedMapTypeLiveData.observeNotNull(this) { mapType: MapType ->
            binding.routeDetailMainContent.avalancheInfo.isVisible = mapType.isAvalancheMap
            avalancheInfoHelper.showAvalancheInfoIfNeeded(mapType, supportFragmentManager)
        }
    }

    override fun showRouteDetails(
        route: Route?,
        watchRouteListFull: Boolean,
        climbGuidance: ClimbGuidance,
        turnByTurnEnabled: Boolean
    ) {
        super.showRouteDetails(route, watchRouteListFull, climbGuidance, turnByTurnEnabled)
        stubBinding.addToWatchView.setup(
            route,
            watchRouteListFull,
            onCheckedChangeListener
        )
    }

    override fun showRouteAltitudeChart(
        chartData: RouteAltitudeChartData,
        avgSpeed: Double,
        climbGuidance: ClimbGuidance
    ) = with(binding.routeDetailBottomSheet.includeTopSummary) {
        routeAltitudeChartWithAxis.updateRouteAltitudeChart(chartData, climbGuidance) { x, y ->
            currentX = x
            routeDetailsPresenter.convertRoutePointsByIndex(chartData, climbGuidance, x, avgSpeed)
            routeAltitudeChartWithAxis.updateMarkerLabelPadding()
            routeAltitudeChartWithAxis.updateMarkerLabel(chartData, y.toString())

            if (routeAltitudeChartWithAxis.isDraggedToEnd()) {
                showClimbGuidanceSegmentOverview(climbGuidance)
            } else {
                routeAltitudeChartWithAxis.getCurrentClimbSegment()
                    ?.let { showCurrentClimbGuidanceSegment(it, climbGuidance) }
            }
        }

        if (needRestore && currentX > 0) {
            routeAltitudeChartWithAxis.moveHighlightToX(currentX.toFloat(), climbGuidance)
            routeDetailsPresenter.convertRoutePointsByIndex(chartData, climbGuidance, currentX, avgSpeed)
            needRestore = false
        } else {
            suuntoMarker?.setVisible(false)
            routeAltitudeChartWithAxis.moveHighlightToEnd(climbGuidance)
        }

        showClimbGuidanceSegmentOverview(climbGuidance)
    }

    private fun showClimbGuidanceSegmentOverview(climbGuidance: ClimbGuidance) {
        if (climbGuidance.isEmpty()) {
            return
        }

        binding.routeDetailBottomSheet.includeTopSummary.routeSections.setContentWithTheme {
            ClimbGuidanceSegmentOverview(climbGuidance)
        }
    }

    private fun showCurrentClimbGuidanceSegment(
        currentClimbSegment: ClimbSegment,
        climbGuidance: ClimbGuidance
    ) {
        if (climbGuidance.isEmpty()) {
            return
        }

        val segmentsOfSameType = climbGuidance.segments
            .map { it.climbSegment }
            .filter { it.climbSegmentType == currentClimbSegment.climbSegmentType }

        binding.routeDetailBottomSheet.includeTopSummary.routeSections.setContentWithTheme {
            ClimbGuidanceSegment(
                currentClimbSegment,
                segmentsOfSameType.indexOf(currentClimbSegment) + 1,
                segmentsOfSameType.count(),
                infoModelFormatter,
            )
        }
    }

    override fun showAddToWatchView() {
        stubBinding.addToWatchViewGroup.visibility = View.VISIBLE
    }

    override fun hideAddToWatch() {
        stubBinding.addToWatchViewGroup.visibility = View.GONE
    }

    override fun onAddRouteToWatchToggled(isChecked: Boolean, route: Route?, source: String) {
        addRouteToWatchViewModel.onAddToWatchToggled(isChecked, route, source)
    }

    override fun onFollowRouteSelected(routeId: String?) = Unit

    override fun onFollowRouteSelectedWhileTracking(routeId: String?, activityType: ActivityType?) = Unit

    override fun setFollowRouteVisibility(visible: Boolean) = Unit

    override fun showRequestPremiumDialog(actionId: Int) = Unit
}
